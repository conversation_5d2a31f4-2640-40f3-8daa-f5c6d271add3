"""
PDF processing module for extracting text content from PDF documents.
"""
import logging
import io
from typing import Optional, <PERSON><PERSON>
import pdfplumber
from PyPDF2 import PdfReader
import re

logger = logging.getLogger(__name__)


class PDFProcessor:
    """Handles PDF text extraction with multiple fallback methods."""
    
    def __init__(self):
        self.max_pages = 50  # Limit processing to first 50 pages for performance
        self.min_text_length = 100  # Minimum text length to consider successful
    
    def extract_text_from_pdf_bytes(self, pdf_bytes: bytes, url: str = "") -> Tuple[Optional[str], Optional[str]]:
        """
        Extract text content from PDF bytes using multiple methods.
        
        Args:
            pdf_bytes: Raw PDF bytes
            url: Source URL for logging
            
        Returns:
            Tuple of (extracted_text, title) or (None, None) if failed
        """
        if not pdf_bytes:
            logger.warning(f"Empty PDF bytes for {url}")
            return None, None
        
        # Try pdfplumber first (better for complex layouts)
        text, title = self._extract_with_pdfplumber(pdf_bytes, url)
        if text and len(text) >= self.min_text_length:
            logger.info(f"Successfully extracted {len(text)} chars from PDF using pdfplumber: {url}")
            return text, title
        
        # Fallback to PyPDF2
        text, title = self._extract_with_pypdf2(pdf_bytes, url)
        if text and len(text) >= self.min_text_length:
            logger.info(f"Successfully extracted {len(text)} chars from PDF using PyPDF2: {url}")
            return text, title
        
        logger.warning(f"Failed to extract sufficient text from PDF: {url}")
        return None, None
    
    def _extract_with_pdfplumber(self, pdf_bytes: bytes, url: str) -> Tuple[Optional[str], Optional[str]]:
        """Extract text using pdfplumber (better for tables and complex layouts)."""
        try:
            pdf_file = io.BytesIO(pdf_bytes)
            
            with pdfplumber.open(pdf_file) as pdf:
                text_parts = []
                title = None
                
                # Extract title from first page or metadata
                if pdf.pages:
                    # Try to get title from metadata
                    if hasattr(pdf, 'metadata') and pdf.metadata:
                        title = pdf.metadata.get('Title', '').strip()
                    
                    # If no title in metadata, try to extract from first page
                    if not title:
                        first_page_text = pdf.pages[0].extract_text()
                        if first_page_text:
                            title = self._extract_title_from_text(first_page_text)
                
                # Extract text from pages (limit to max_pages for performance)
                pages_to_process = min(len(pdf.pages), self.max_pages)
                
                for i, page in enumerate(pdf.pages[:pages_to_process]):
                    try:
                        page_text = page.extract_text()
                        if page_text:
                            # Clean and add page text
                            cleaned_text = self._clean_pdf_text(page_text)
                            if cleaned_text:
                                text_parts.append(cleaned_text)
                    except Exception as e:
                        logger.warning(f"Failed to extract text from page {i+1} of {url}: {e}")
                        continue
                
                if text_parts:
                    full_text = '\n\n'.join(text_parts)
                    return full_text, title
                
        except Exception as e:
            logger.warning(f"pdfplumber extraction failed for {url}: {e}")
        
        return None, None
    
    def _extract_with_pypdf2(self, pdf_bytes: bytes, url: str) -> Tuple[Optional[str], Optional[str]]:
        """Extract text using PyPDF2 (fallback method)."""
        try:
            pdf_file = io.BytesIO(pdf_bytes)
            reader = PdfReader(pdf_file)
            
            text_parts = []
            title = None
            
            # Try to get title from metadata
            if reader.metadata:
                title = reader.metadata.get('/Title', '').strip()
            
            # Extract text from pages
            pages_to_process = min(len(reader.pages), self.max_pages)
            
            for i, page in enumerate(reader.pages[:pages_to_process]):
                try:
                    page_text = page.extract_text()
                    if page_text:
                        # Clean and add page text
                        cleaned_text = self._clean_pdf_text(page_text)
                        if cleaned_text:
                            text_parts.append(cleaned_text)
                            
                            # Extract title from first page if not found in metadata
                            if not title and i == 0:
                                title = self._extract_title_from_text(page_text)
                                
                except Exception as e:
                    logger.warning(f"Failed to extract text from page {i+1} of {url}: {e}")
                    continue
            
            if text_parts:
                full_text = '\n\n'.join(text_parts)
                return full_text, title
                
        except Exception as e:
            logger.warning(f"PyPDF2 extraction failed for {url}: {e}")
        
        return None, None
    
    def _clean_pdf_text(self, text: str) -> str:
        """Clean extracted PDF text."""
        if not text:
            return ""
        
        # Remove excessive whitespace and normalize line breaks
        text = re.sub(r'\s+', ' ', text)
        text = re.sub(r'\n\s*\n', '\n\n', text)
        
        # Remove common PDF artifacts
        text = re.sub(r'[^\w\s\-.,;:()!?@#$%&*+=<>/\[\]{}|\\~`"\']+', ' ', text)
        
        # Remove very short lines (likely artifacts)
        lines = text.split('\n')
        cleaned_lines = []
        for line in lines:
            line = line.strip()
            if len(line) > 3:  # Keep lines with more than 3 characters
                cleaned_lines.append(line)
        
        return '\n'.join(cleaned_lines)
    
    def _extract_title_from_text(self, text: str) -> str:
        """Extract title from the beginning of PDF text."""
        if not text:
            return ""
        
        # Look for title-like patterns in first few lines
        lines = text.split('\n')[:10]  # Check first 10 lines
        
        for line in lines:
            line = line.strip()
            # Skip very short lines or lines with mostly numbers/symbols
            if len(line) < 10 or len(line) > 200:
                continue
            
            # Look for title patterns (often in caps or title case)
            if (line.isupper() or line.istitle()) and len(line.split()) >= 2:
                return line
        
        # Fallback: return first substantial line
        for line in lines:
            line = line.strip()
            if len(line) >= 10 and len(line) <= 100:
                return line
        
        return ""
    
    @staticmethod
    def is_pdf_url(url: str) -> bool:
        """Check if URL likely points to a PDF file."""
        url_lower = url.lower()
        return (
            url_lower.endswith('.pdf') or 
            '.pdf?' in url_lower or 
            'pdf' in url_lower.split('/')[-1] or
            'filetype:pdf' in url_lower
        )
    
    @staticmethod
    def is_pdf_content_type(content_type: str) -> bool:
        """Check if content type indicates PDF."""
        if not content_type:
            return False
        return 'application/pdf' in content_type.lower()
