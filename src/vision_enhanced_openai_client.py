"""
Vision-Enhanced OpenAI Extraction Client for multimodal power plant data extraction.
Leverages GPT-4o-mini's vision capabilities to process both text and image-based PDFs.
"""

import asyncio
import json
import logging
import base64
import io
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass
import openai
from openai import AsyncOpenAI
from datetime import datetime
from pdf2image import convert_from_bytes
from PIL import Image
import fitz  # PyMuPDF for better PDF handling

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class VisionExtractionResult:
    """Result of vision-enhanced field extraction with confidence score."""
    extracted_value: Any
    confidence_score: float
    source_info: str = ""
    extraction_method: str = "openai_vision"
    document_type: str = "unknown"  # text_based, scanned, hybrid
    pages_processed: int = 0

class VisionEnhancedOpenAIClient:
    """
    Vision-enhanced OpenAI client using GPT-4o-mini for multimodal data extraction.
    Handles both text-based and scanned/image-based PDF documents.
    """

    def __init__(self, api_key: str, model: str = "gpt-4o-mini"):
        """
        Initialize vision-enhanced OpenAI extraction client.

        Args:
            api_key: OpenAI API key
            model: OpenAI model to use (gpt-4o-mini for vision capabilities)
        """
        self.client = AsyncOpenAI(api_key=api_key)
        self.model = model
        self.extraction_count = 0
        self.vision_extraction_count = 0
        self.total_tokens_used = 0
        self.max_image_size = (1024, 1024)  # Optimize for API limits
        self.max_pages_per_extraction = 5   # Limit pages for cost control

        logger.info(f"Vision-enhanced OpenAI client initialized with model: {model}")

    def detect_pdf_type(self, pdf_bytes: bytes, text_content: str = None) -> str:
        """
        Detect PDF type to choose optimal extraction strategy.

        Args:
            pdf_bytes: Raw PDF bytes
            text_content: Pre-extracted text content (optional)

        Returns:
            PDF type: 'text_based', 'scanned', or 'hybrid'
        """
        try:
            # If text content provided, analyze it
            if text_content:
                text_length = len(text_content.strip())
                word_count = len(text_content.split())

                if text_length > 2000 and word_count > 300:
                    return "text_based"
                elif text_length < 100 and word_count < 20:
                    return "scanned"
                else:
                    return "hybrid"

            # Fallback: Try to extract text and analyze
            try:
                doc = fitz.open(stream=pdf_bytes, filetype="pdf")
                total_text = ""

                for page_num in range(min(3, len(doc))):  # Check first 3 pages
                    page = doc.load_page(page_num)
                    total_text += page.get_text()

                doc.close()

                text_length = len(total_text.strip())
                if text_length > 1000:
                    return "text_based"
                elif text_length < 50:
                    return "scanned"
                else:
                    return "hybrid"

            except Exception as e:
                logger.warning(f"PDF analysis failed: {e}")
                return "hybrid"  # Default to hybrid for safety

        except Exception as e:
            logger.error(f"PDF type detection failed: {e}")
            return "hybrid"

    def convert_pdf_to_images(self, pdf_bytes: bytes, max_pages: int = None) -> List[Image.Image]:
        """
        Convert PDF pages to images for vision processing.

        Args:
            pdf_bytes: Raw PDF bytes
            max_pages: Maximum pages to convert (default: self.max_pages_per_extraction)

        Returns:
            List of PIL Images
        """
        try:
            max_pages = max_pages or self.max_pages_per_extraction

            # Convert PDF to images
            images = convert_from_bytes(
                pdf_bytes,
                first_page=1,
                last_page=max_pages,
                dpi=200,  # Good balance of quality vs size
                fmt='JPEG'
            )

            # Resize images to optimize for API
            optimized_images = []
            for img in images:
                if img.size[0] > self.max_image_size[0] or img.size[1] > self.max_image_size[1]:
                    img.thumbnail(self.max_image_size, Image.Resampling.LANCZOS)
                optimized_images.append(img)

            logger.info(f"Converted {len(optimized_images)} PDF pages to images")
            return optimized_images

        except Exception as e:
            logger.error(f"PDF to image conversion failed: {e}")
            return []

    def image_to_base64(self, image: Image.Image) -> str:
        """Convert PIL Image to base64 string for API."""
        try:
            buffer = io.BytesIO()
            image.save(buffer, format='JPEG', quality=85)
            img_bytes = buffer.getvalue()
            return base64.b64encode(img_bytes).decode('utf-8')
        except Exception as e:
            logger.error(f"Image to base64 conversion failed: {e}")
            return ""

    async def extract_field_vision(self, field_name: str, pdf_bytes: bytes, context: str = "") -> Optional[VisionExtractionResult]:
        """
        Extract field using vision capabilities for scanned/image PDFs.

        Args:
            field_name: Name of the field to extract
            pdf_bytes: Raw PDF bytes
            context: Additional context for extraction

        Returns:
            VisionExtractionResult with extracted value and metadata
        """
        try:
            self.vision_extraction_count += 1

            # Convert PDF to images
            images = self.convert_pdf_to_images(pdf_bytes)
            if not images:
                logger.warning("No images extracted from PDF for vision processing")
                return None

            # Create vision extraction prompt
            prompt = self._create_vision_extraction_prompt(field_name, context)

            # Prepare messages with images
            message_content = [
                {
                    "type": "text",
                    "text": prompt
                }
            ]

            # Add images to message (limit to avoid token limits)
            for i, image in enumerate(images[:3]):  # Max 3 images per request
                base64_image = self.image_to_base64(image)
                if base64_image:
                    message_content.append({
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{base64_image}",
                            "detail": "high"  # High detail for technical documents
                        }
                    })

            # Call OpenAI Vision API
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert at extracting technical information from power plant documents, including scanned PDFs, charts, tables, and technical diagrams. Analyze all provided images carefully."
                    },
                    {
                        "role": "user",
                        "content": message_content
                    }
                ],
                temperature=0.1,
                max_tokens=800,
                response_format={"type": "json_object"}
            )

            # Track token usage
            if hasattr(response, 'usage') and response.usage:
                self.total_tokens_used += response.usage.total_tokens

            # Parse response
            result_text = response.choices[0].message.content
            result_json = json.loads(result_text)

            # Extract value and confidence
            extracted_value = result_json.get("value")
            confidence = float(result_json.get("confidence", 0.0))
            reasoning = result_json.get("reasoning", "")

            logger.info(f"Vision extracted {field_name}: {str(extracted_value)[:50]}{'...' if len(str(extracted_value)) > 50 else ''} (confidence: {confidence:.2f})")

            return VisionExtractionResult(
                extracted_value=extracted_value,
                confidence_score=confidence,
                source_info=reasoning,
                extraction_method=f"openai_vision_{self.model}",
                document_type="scanned",
                pages_processed=len(images)
            )

        except json.JSONDecodeError as e:
            logger.error(f"JSON parsing failed for vision extraction {field_name}: {e}")
            return None
        except Exception as e:
            logger.error(f"Vision extraction failed for field {field_name}: {e}")
            return None

    async def extract_field_hybrid(self, field_name: str, pdf_bytes: bytes, text_content: str = None, context: str = "") -> Optional[VisionExtractionResult]:
        """
        Hybrid extraction: try text first, fallback to vision if needed.

        Args:
            field_name: Name of the field to extract
            pdf_bytes: Raw PDF bytes
            text_content: Pre-extracted text content (optional)
            context: Additional context for extraction

        Returns:
            VisionExtractionResult with best extraction result
        """
        try:
            # Detect PDF type
            pdf_type = self.detect_pdf_type(pdf_bytes, text_content)
            logger.info(f"Detected PDF type: {pdf_type} for field {field_name}")

            # Strategy 1: Text-based extraction (faster, cheaper)
            if pdf_type in ["text_based", "hybrid"] and text_content:
                text_result = await self.extract_field_text(field_name, text_content, context)

                # If text extraction is confident enough, use it
                if text_result and text_result.confidence_score >= 0.7:
                    text_result.document_type = pdf_type
                    logger.info(f"Text extraction successful for {field_name} (confidence: {text_result.confidence_score:.2f})")
                    return text_result

            # Strategy 2: Vision extraction (for scanned or failed text extraction)
            if pdf_type in ["scanned", "hybrid"] or (text_result and text_result.confidence_score < 0.7):
                logger.info(f"Attempting vision extraction for {field_name}")
                vision_result = await self.extract_field_vision(field_name, pdf_bytes, context)

                if vision_result:
                    vision_result.document_type = pdf_type
                    return vision_result

            # Fallback: return text result even if low confidence
            if text_result:
                text_result.document_type = pdf_type
                return text_result

            return None

        except Exception as e:
            logger.error(f"Hybrid extraction failed for field {field_name}: {e}")
            return None

    async def extract_field_text(self, field_name: str, content: str, context: str = "") -> Optional[VisionExtractionResult]:
        """
        Extract field from text content (traditional method).

        Args:
            field_name: Name of the field to extract
            content: Text content to extract from
            context: Additional context for extraction

        Returns:
            VisionExtractionResult with extracted value
        """
        try:
            self.extraction_count += 1

            # Create text extraction prompt
            prompt = self._create_text_extraction_prompt(field_name, content, context)

            # Call OpenAI API
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert power plant data extraction specialist. Extract precise technical information from text documents with high accuracy."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.1,
                max_tokens=500,
                response_format={"type": "json_object"}
            )

            # Track token usage
            if hasattr(response, 'usage') and response.usage:
                self.total_tokens_used += response.usage.total_tokens

            # Parse response
            result_text = response.choices[0].message.content
            result_json = json.loads(result_text)

            # Extract value and confidence
            extracted_value = result_json.get("value")
            confidence = float(result_json.get("confidence", 0.0))
            reasoning = result_json.get("reasoning", "")

            return VisionExtractionResult(
                extracted_value=extracted_value,
                confidence_score=confidence,
                source_info=reasoning,
                extraction_method=f"openai_text_{self.model}",
                document_type="text_based",
                pages_processed=0
            )

        except Exception as e:
            logger.error(f"Text extraction failed for field {field_name}: {e}")
            return None

    def _create_vision_extraction_prompt(self, field_name: str, context: str) -> str:
        """Create optimized prompt for vision-based extraction."""

        # Field-specific vision instructions
        vision_instructions = {
            "cfpp_type": "Look for ownership type of the power plant company in corporate documents, annual reports, or project descriptions (e.g., 'private', 'public', 'joint-venture', 'government', 'state-owned').",
            "financial_year": "Find the fiscal year period format for the country where plant is located in financial documents or annual reports (e.g., '04-03' for India, '01-12' for USA, '04-03' for UK, '07-06' for Australia).",
            "capacity": "Look for unit capacity in MW in tables, specifications, or technical diagrams. Check headers, captions, and data tables.",
            "technology": "Identify power generation technology from technical specifications, equipment lists, or process diagrams (e.g., Supercritical, Subcritical).",
            "heat_rate": "Find station heat rate or unit heat rate values in performance tables, efficiency charts, or technical specifications (kcal/kWh, BTU/kWh).",
            "commencement_date": "Look for commercial operation date (COD), commissioning date, or start date in project timelines, certificates, or status tables.",
            "coordinates": "Extract latitude/longitude coordinates from maps, location diagrams, or geographical information tables.",
            "grid_connectivity_maps": "Find grid connectivity as nested JSON with details containing substation capacity, coordinates, projects, substation names and types from network diagrams or technical tables.",
            "ppa_details": "Find Power Purchase Agreement details as nested JSON with capacity, dates, respondents (names, prices, currencies), tenure from contract tables or agreement summaries.",
            "grid_connectivity": "Look for transmission line details, substation connections, or grid integration information in network diagrams or technical tables."
        }

        instruction = vision_instructions.get(field_name, f"Extract the value for {field_name} from any visible text, tables, charts, or diagrams in the images.")

        prompt = f"""
Analyze the provided power plant document images and extract the field "{field_name}".

FIELD: {field_name}
INSTRUCTION: {instruction}
CONTEXT: {context}

VISION ANALYSIS TASKS:
1. Scan all visible text in the images
2. Examine tables, charts, and technical diagrams
3. Look for relevant data in headers, captions, and footnotes
4. Check for information in multiple pages if provided
5. Consider technical drawings and schematic diagrams

EXTRACTION RULES:
1. Extract ONLY the specific value requested
2. For numerical values, include units if they're part of the field
3. For dates, use ISO format (YYYY-MM-DD) when possible
4. If information spans multiple images, combine relevant details
5. If the exact value is not visible, return null
6. Provide confidence based on clarity and visibility of the information

RESPONSE FORMAT (JSON):
{{
    "value": <extracted_value_or_null>,
    "confidence": <confidence_score_0_to_1>,
    "reasoning": "<explanation_of_what_you_found_and_where>"
}}

Analyze the images and extract the value now:
"""
        return prompt

    def _create_text_extraction_prompt(self, field_name: str, content: str, context: str) -> str:
        """Create optimized prompt for text-based extraction."""

        # Field-specific text instructions
        field_instructions = {
            "cfpp_type": "Extract the ownership type of the power plant company (e.g., 'private', 'public', 'joint-venture', 'government', 'state-owned'). Look for corporate structure, ownership details, or company type information.",
            "financial_year": "Extract the fiscal year period format for the country where plant is located (e.g., '04-03' for India, '01-12' for USA, '04-03' for UK, '07-06' for Australia). Look for financial reporting periods or fiscal year mentions.",
            "capacity": "Extract the unit capacity in MW. Look for specific unit capacity, not total plant capacity.",
            "capacity_unit": "Extract the unit of measurement for capacity (usually MW).",
            "technology": "Extract the power generation technology (e.g., Supercritical, Subcritical, Ultra Supercritical).",
            "fuel_type": "Extract the primary fuel type and create a structured list with fuel details.",
            "heat_rate": "Extract the station heat rate or unit heat rate in kcal/kWh or BTU/kWh.",
            "unit_efficiency": "Extract the thermal efficiency percentage of the unit.",
            "commencement_date": "Extract the commercial operation date (COD) or commissioning date.",
            "boiler_type": "Extract the boiler technology type (e.g., Pulverized Coal, Circulating Fluidized Bed).",
            "plf": "Extract the Plant Load Factor (PLF) or Capacity Utilization Factor (CUF) as percentage.",
            "emission_factor": "Extract CO2 emission factor in kg CO2/MWh or similar units.",
            "grid_connectivity_maps": "Extract grid connectivity as nested JSON array with details containing substation capacity, coordinates, projects, substation names and types.",
            "ppa_details": "Extract Power Purchase Agreement details as nested JSON array with capacity, dates, respondents (names, prices, currencies), tenure information."
        }

        instruction = field_instructions.get(field_name, f"Extract the value for {field_name} from the content.")

        # Truncate content to avoid token limits
        max_content_length = 8000
        if len(content) > max_content_length:
            content = content[:max_content_length] + "... [content truncated]"

        prompt = f"""
Extract the field "{field_name}" from the following power plant technical content.

FIELD: {field_name}
INSTRUCTION: {instruction}
CONTEXT: {context}

CONTENT:
{content}

EXTRACTION RULES:
1. Extract ONLY the specific value requested
2. For numerical values, extract just the number without units unless units are part of the field
3. For dates, use ISO format (YYYY-MM-DD) when possible
4. For lists/arrays, structure them properly as JSON arrays
5. If the exact value is not found, return null
6. Provide a confidence score from 0.0 to 1.0 based on how certain you are

RESPONSE FORMAT (JSON):
{{
    "value": <extracted_value_or_null>,
    "confidence": <confidence_score_0_to_1>,
    "reasoning": "<brief_explanation_of_extraction>"
}}

Extract the value now:
"""
        return prompt

    async def extract_multiple_fields_vision(self, field_list: List[str], pdf_bytes: bytes, context: str = "") -> Dict[str, VisionExtractionResult]:
        """
        Extract multiple fields using vision capabilities in a single API call.

        Args:
            field_list: List of field names to extract
            pdf_bytes: Raw PDF bytes
            context: Additional context

        Returns:
            Dictionary mapping field names to VisionExtractionResults
        """
        try:
            # Convert PDF to images
            images = self.convert_pdf_to_images(pdf_bytes)
            if not images:
                return {}

            # Create batch vision prompt
            prompt = self._create_batch_vision_prompt(field_list, context)

            # Prepare messages with images
            message_content = [{"type": "text", "text": prompt}]

            # Add images (limit to 3 for cost control)
            for image in images[:3]:
                base64_image = self.image_to_base64(image)
                if base64_image:
                    message_content.append({
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{base64_image}",
                            "detail": "high"
                        }
                    })

            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert at extracting multiple technical fields from power plant documents, including scanned PDFs and technical diagrams."
                    },
                    {
                        "role": "user",
                        "content": message_content
                    }
                ],
                temperature=0.1,
                max_tokens=2000,
                response_format={"type": "json_object"}
            )

            # Track token usage
            if hasattr(response, 'usage') and response.usage:
                self.total_tokens_used += response.usage.total_tokens

            # Parse response
            result_text = response.choices[0].message.content
            result_json = json.loads(result_text)

            # Convert to VisionExtractionResult objects
            results = {}
            for field_name in field_list:
                field_data = result_json.get(field_name, {})
                if field_data:
                    results[field_name] = VisionExtractionResult(
                        extracted_value=field_data.get("value"),
                        confidence_score=float(field_data.get("confidence", 0.0)),
                        source_info=field_data.get("reasoning", ""),
                        extraction_method=f"openai_vision_{self.model}_batch",
                        document_type="scanned",
                        pages_processed=len(images)
                    )

            logger.info(f"Vision batch extracted {len(results)} fields successfully")
            return results

        except Exception as e:
            logger.error(f"Vision batch extraction failed: {e}")
            return {}

    def _create_batch_vision_prompt(self, field_list: List[str], context: str) -> str:
        """Create prompt for extracting multiple fields from images."""

        prompt = f"""
Analyze the provided power plant document images and extract the following fields:

FIELDS TO EXTRACT: {field_list}
CONTEXT: {context}

VISION ANALYSIS TASKS:
1. Scan all visible text, tables, charts, and diagrams in all provided images
2. Look for technical specifications, performance data, and project details
3. Check headers, captions, footnotes, and technical drawings
4. Combine information from multiple pages if needed

EXTRACTION RULES:
1. Extract each field value precisely from visible content
2. Return null if field value is not visible in any image
3. Provide confidence score (0.0-1.0) for each field based on visibility and clarity
4. Structure numerical values appropriately with units when relevant

RESPONSE FORMAT (JSON):
{{
    "field_name": {{
        "value": <extracted_value_or_null>,
        "confidence": <confidence_score>,
        "reasoning": "<explanation_of_what_you_found_and_where>"
    }},
    ...
}}

Analyze all images and extract all fields now:
"""
        return prompt

    def get_usage_stats(self) -> Dict[str, Any]:
        """Get comprehensive usage statistics."""
        return {
            "total_text_extractions": self.extraction_count,
            "total_vision_extractions": self.vision_extraction_count,
            "total_extractions": self.extraction_count + self.vision_extraction_count,
            "total_tokens_used": self.total_tokens_used,
            "model": self.model,
            "vision_enabled": True,
            "timestamp": datetime.now().isoformat()
        }

    async def close(self):
        """Close the client connection."""
        await self.client.close()
        logger.info(f"Vision-enhanced OpenAI client closed. Text: {self.extraction_count}, Vision: {self.vision_extraction_count}, Total tokens: {self.total_tokens_used}")