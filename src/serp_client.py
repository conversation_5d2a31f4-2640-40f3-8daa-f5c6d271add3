"""
SERP API client for power plant information discovery.
"""
import asyncio
import aiohttp
import logging
from typing import List, Dict, Optional
from tenacity import retry, stop_after_attempt, wait_exponential
from src.models import SearchResult
from src.config import config

logger = logging.getLogger(__name__)


class SerpAPIClient:
    """Client for ScraperAPI structured search integration."""

    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.scraperapi.com/structured/google/search"
        self.session = None

    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=config.pipeline.request_timeout)
        )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    async def search(
        self,
        query: str,
        num_results: int = 10
    ) -> List[SearchResult]:
        """
        Perform search using ScraperAPI structured search.

        Args:
            query: Search query string
            num_results: Number of results to return

        Returns:
            List of SearchResult objects
        """
        if not self.session:
            raise RuntimeError("SerpAPIClient must be used as async context manager")

        params = {
            "api_key": self.api_key,
            "query": query,
            "num": num_results,
            "page": 1
        }

        try:
            async with self.session.get(self.base_url, params=params) as response:
                response.raise_for_status()
                data = await response.json()

                return self._parse_search_results(data, query)

        except aiohttp.ClientError as e:
            logger.error(f"SERP API request failed: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error in SERP API search: {e}")
            raise

    def _parse_search_results(self, data: Dict, query: str) -> List[SearchResult]:
        """Parse ScraperAPI structured search response into SearchResult objects."""
        results = []

        # Parse organic results from ScraperAPI response
        organic_results = data.get("organic_results", [])

        for i, result in enumerate(organic_results):
            # ScraperAPI uses 'link' for URL and 'snippet' for description
            search_result = SearchResult(
                title=result.get("title", ""),
                url=result.get("link", ""),
                snippet=result.get("snippet", ""),
                rank=i + 1,
                source_type=self._identify_source_type(result.get("link", "")),
                relevance_score=self._calculate_relevance_score(
                    result.get("title", "") + " " + result.get("snippet", ""),
                    query
                )
            )
            results.append(search_result)

        # Sort by relevance score and source type priority
        results.sort(
            key=lambda x: (
                config.url_priority_weights.get(x.source_type, 0),
                x.relevance_score
            ),
            reverse=True
        )

        return results

    def _identify_source_type(self, url: str) -> str:
        """Identify the type of source based on URL patterns."""
        url_lower = url.lower()

        for source_type, indicators in config.source_type_indicators.items():
            if any(indicator in url_lower for indicator in indicators):
                return source_type

        return "other"

    def _calculate_relevance_score(self, text: str, query: str) -> float:
        """Calculate relevance score based on keyword matching."""
        text_lower = text.lower()
        query_lower = query.lower()

        # Base score for query term presence
        score = 0.0
        query_terms = query_lower.split()

        for term in query_terms:
            if term in text_lower:
                score += 0.1

        # Bonus for high relevance keywords
        for keyword in config.content_relevance_keywords["high_relevance"]:
            if keyword in text_lower:
                score += 0.2

        # Bonus for medium relevance keywords
        for keyword in config.content_relevance_keywords["medium_relevance"]:
            if keyword in text_lower:
                score += 0.1

        return min(score, 1.0)  # Cap at 1.0


class PowerPlantSearchOrchestrator:
    """Orchestrates multi-stage search for power plant information."""

    def __init__(self, serp_client: SerpAPIClient):
        self.serp_client = serp_client

    async def comprehensive_search(self, plant_name: str) -> Dict[str, List[SearchResult]]:
        """
        Perform comprehensive multi-stage search for power plant information.

        Args:
            plant_name: Name of the power plant to search for

        Returns:
            Dictionary with search results categorized by search type
        """
        search_results = {}

        # Stage 1: Basic discovery
        logger.info(f"Starting basic discovery search for: {plant_name}")
        basic_queries = [
            template.format(plant_name=plant_name)
            for template in config.search_query_templates["basic_discovery"]
        ]

        search_results["basic_discovery"] = []
        for query in basic_queries:
            try:
                results = await self.serp_client.search(query, num_results=8)
                search_results["basic_discovery"].extend(results)
                await asyncio.sleep(1)  # Rate limiting
            except Exception as e:
                logger.error(f"Search failed for query '{query}': {e}")

        # Stage 2: Organizational search
        logger.info(f"Starting organizational search for: {plant_name}")
        org_queries = [
            template.format(plant_name=plant_name)
            for template in config.search_query_templates["organizational"][:3]  # Skip org-specific query for now
        ]

        search_results["organizational"] = []
        for query in org_queries:
            try:
                results = await self.serp_client.search(query, num_results=6)
                search_results["organizational"].extend(results)
                await asyncio.sleep(1)
            except Exception as e:
                logger.error(f"Organizational search failed for query '{query}': {e}")

        # Stage 3: Technical details
        logger.info(f"Starting technical search for: {plant_name}")
        tech_queries = [
            template.format(plant_name=plant_name)
            for template in config.search_query_templates["technical_details"]
        ]

        search_results["technical_details"] = []
        for query in tech_queries:
            try:
                results = await self.serp_client.search(query, num_results=5)
                search_results["technical_details"].extend(results)
                await asyncio.sleep(1)
            except Exception as e:
                logger.error(f"Technical search failed for query '{query}': {e}")

        # Stage 4: Location details
        logger.info(f"Starting location search for: {plant_name}")
        location_queries = [
            template.format(plant_name=plant_name)
            for template in config.search_query_templates["location_details"][:2]  # Skip location-specific for now
        ]

        search_results["location_details"] = []
        for query in location_queries:
            try:
                results = await self.serp_client.search(query, num_results=5)
                search_results["location_details"].extend(results)
                await asyncio.sleep(1)
            except Exception as e:
                logger.error(f"Location search failed for query '{query}': {e}")

        # Stage 5: PPA details
        logger.info(f"Starting PPA search for: {plant_name}")
        ppa_queries = [
            template.format(plant_name=plant_name)
            for template in config.search_query_templates["ppa_details"]
        ]

        search_results["ppa_details"] = []
        for query in ppa_queries:
            try:
                results = await self.serp_client.search(query, num_results=4)
                search_results["ppa_details"].extend(results)
                await asyncio.sleep(1)
            except Exception as e:
                logger.error(f"PPA search failed for query '{query}': {e}")

        # Deduplicate results across all categories
        search_results = self._deduplicate_results(search_results)

        logger.info(f"Comprehensive search completed. Total unique URLs found: {self._count_unique_urls(search_results)}")

        return search_results

    async def comprehensive_plant_details_search(self, plant_name: str) -> Dict[str, List[SearchResult]]:
        """
        Perform comprehensive search specifically for plant technical details.

        Args:
            plant_name: Name of the power plant to search for

        Returns:
            Dictionary with search results categorized by technical search type
        """
        search_results = {}

        # Stage 1: Grid connectivity search
        logger.info(f"Starting grid connectivity search for: {plant_name}")
        grid_queries = [
            template.format(plant_name=plant_name)
            for template in config.search_query_templates["grid_connectivity"]
        ]

        search_results["grid_connectivity"] = []
        for query in grid_queries:
            try:
                results = await self.serp_client.search(query, num_results=5)
                search_results["grid_connectivity"].extend(results)
                await asyncio.sleep(1)  # Rate limiting
            except Exception as e:
                logger.error(f"Grid connectivity search failed for query '{query}': {e}")

        # Stage 2: Plant coordinates search
        logger.info(f"Starting coordinates search for: {plant_name}")
        coord_queries = [
            template.format(plant_name=plant_name)
            for template in config.search_query_templates["plant_coordinates"]
        ]

        search_results["plant_coordinates"] = []
        for query in coord_queries:
            try:
                results = await self.serp_client.search(query, num_results=4)
                search_results["plant_coordinates"].extend(results)
                await asyncio.sleep(1)
            except Exception as e:
                logger.error(f"Coordinates search failed for query '{query}': {e}")

        # Stage 3: PPA contracts search
        logger.info(f"Starting PPA contracts search for: {plant_name}")
        ppa_queries = [
            template.format(plant_name=plant_name)
            for template in config.search_query_templates["ppa_contracts"]
        ]

        search_results["ppa_contracts"] = []
        for query in ppa_queries:
            try:
                results = await self.serp_client.search(query, num_results=5)
                search_results["ppa_contracts"].extend(results)
                await asyncio.sleep(1)
            except Exception as e:
                logger.error(f"PPA contracts search failed for query '{query}': {e}")

        # Stage 4: Plant units search
        logger.info(f"Starting plant units search for: {plant_name}")
        units_queries = [
            template.format(plant_name=plant_name)
            for template in config.search_query_templates["plant_units"]
        ]

        search_results["plant_units"] = []
        for query in units_queries:
            try:
                results = await self.serp_client.search(query, num_results=4)
                search_results["plant_units"].extend(results)
                await asyncio.sleep(1)
            except Exception as e:
                logger.error(f"Plant units search failed for query '{query}': {e}")

        # Stage 5: Plant specifications search
        logger.info(f"Starting specifications search for: {plant_name}")
        spec_queries = [
            template.format(plant_name=plant_name)
            for template in config.search_query_templates["plant_specifications"]
        ]

        search_results["plant_specifications"] = []
        for query in spec_queries:
            try:
                results = await self.serp_client.search(query, num_results=4)
                search_results["plant_specifications"].extend(results)
                await asyncio.sleep(1)
            except Exception as e:
                logger.error(f"Plant specifications search failed for query '{query}': {e}")

        # Deduplicate results across all categories
        search_results = self._deduplicate_results(search_results)

        logger.info(f"Plant details search completed. Total unique URLs found: {self._count_unique_urls(search_results)}")

        return search_results

    async def search_power_plant_comprehensive(
        self,
        plant_name: str,
        max_results_per_category: int = 5
    ) -> List[SearchResult]:
        """
        Comprehensive search for power plant information (alias for comprehensive_search).

        Args:
            plant_name: Name of the power plant
            max_results_per_category: Maximum results per search category

        Returns:
            Flattened list of all search results
        """
        search_results_dict = await self.comprehensive_search(plant_name)

        # Flatten all results into a single list
        all_results = []
        for category, results in search_results_dict.items():
            all_results.extend(results[:max_results_per_category])

        return all_results

    async def search_power_plant_technical(
        self,
        plant_name: str,
        max_results_per_category: int = 5
    ) -> List[SearchResult]:
        """
        Technical search for power plant details (alias for comprehensive_plant_details_search).

        Args:
            plant_name: Name of the power plant
            max_results_per_category: Maximum results per search category

        Returns:
            Flattened list of technical search results
        """
        search_results_dict = await self.comprehensive_plant_details_search(plant_name)

        # Flatten all results into a single list
        all_results = []
        for category, results in search_results_dict.items():
            all_results.extend(results[:max_results_per_category])

        return all_results

    async def search_specific_field(
        self,
        search_query: str,
        max_results: int = 3
    ) -> List[SearchResult]:
        """
        Search for a specific field or information.

        Args:
            search_query: Specific search query
            max_results: Maximum number of results to return

        Returns:
            List of search results
        """
        try:
            results = await self.serp_client.search(search_query, num_results=max_results)
            return results
        except Exception as e:
            logger.error(f"Specific field search failed for query '{search_query}': {e}")
            return []

    def _deduplicate_results(self, search_results: Dict[str, List[SearchResult]]) -> Dict[str, List[SearchResult]]:
        """Remove duplicate URLs across search categories."""
        seen_urls = set()
        deduplicated = {}

        for category, results in search_results.items():
            unique_results = []
            for result in results:
                if result.url not in seen_urls:
                    seen_urls.add(result.url)
                    unique_results.append(result)
            deduplicated[category] = unique_results

        return deduplicated

    def _count_unique_urls(self, search_results: Dict[str, List[SearchResult]]) -> int:
        """Count total unique URLs across all categories."""
        all_urls = set()
        for results in search_results.values():
            for result in results:
                all_urls.add(result.url)
        return len(all_urls)
