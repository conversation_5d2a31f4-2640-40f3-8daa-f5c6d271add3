"""
Unified pipeline for extracting both organizational and plant technical details.
"""
import asyncio
import logging
import json
from typing import Dict, List, Optional, Tuple
from datetime import datetime

from src.models import OrganizationalDetails, PlantDetails, ScrapedContent
from src.config import config
from src.serp_client import SerpAP<PERSON><PERSON>, PowerPlantSearchOrchestrator
from src.scraper_client import ScraperAPIClient, ContentScrapeOrchestrator
from src.groq_client import GroqExtractionClient
from src.enhanced_extractor import AdaptiveExtractor
from src.plant_details_extractor import PlantDetailsExtractor

logger = logging.getLogger(__name__)


class UnifiedPowerPlantPipeline:
    """Unified pipeline for extracting both organizational and plant technical details."""

    def __init__(self):
        self.serp_api_key = config.pipeline.serp_api_key
        self.scraper_api_key = config.pipeline.scraper_api_key
        self.groq_api_key = config.pipeline.groq_api_key

        # Validate API keys
        if not self.scraper_api_key:
            raise ValueError("SCRAPER_API_KEY is required")
        if not self.groq_api_key:
            raise ValueError("GROQ_API_KEY is required")

        logger.info(f"Unified pipeline initialized with API keys configured")

    async def extract_complete_plant_data(
        self, 
        plant_name: str,
        extract_organizational: bool = True,
        extract_technical: bool = True
    ) -> Tuple[Optional[OrganizationalDetails], Optional[PlantDetails]]:
        """
        Extract both organizational and technical details for a power plant.

        Args:
            plant_name: Name of the power plant
            extract_organizational: Whether to extract organizational details
            extract_technical: Whether to extract technical details

        Returns:
            Tuple of (OrganizationalDetails, PlantDetails) objects
        """
        logger.info(f"Starting unified data extraction pipeline for: {plant_name}")

        org_details = None
        plant_details = None

        try:
            # Phase 1: Comprehensive Search
            all_search_results = await self._unified_search_phase(
                plant_name, extract_organizational, extract_technical
            )

            # Phase 2: Content Scraping
            scraped_contents = await self._scraping_phase(all_search_results)

            # Phase 3: Parallel Extraction
            if extract_organizational and extract_technical:
                org_details, plant_details = await self._parallel_extraction_phase(
                    scraped_contents, plant_name
                )
            elif extract_organizational:
                org_details = await self._organizational_extraction_phase(
                    scraped_contents, plant_name
                )
            elif extract_technical:
                plant_details = await self._technical_extraction_phase(
                    scraped_contents, plant_name
                )

            logger.info(f"Unified pipeline completed successfully for: {plant_name}")

        except Exception as e:
            logger.error(f"Unified pipeline failed for {plant_name}: {e}")
            raise

        return org_details, plant_details

    async def _unified_search_phase(
        self, 
        plant_name: str,
        extract_organizational: bool,
        extract_technical: bool
    ) -> Dict:
        """Phase 1: Unified search for both organizational and technical information."""
        logger.info("Phase 1: Starting unified search phase")

        async with SerpAPIClient(self.serp_api_key) as serp_client:
            search_orchestrator = PowerPlantSearchOrchestrator(serp_client)
            
            all_search_results = {}

            # Organizational search
            if extract_organizational:
                logger.info("Performing organizational search...")
                org_results = await search_orchestrator.comprehensive_search(plant_name)
                all_search_results.update(org_results)

            # Technical details search
            if extract_technical:
                logger.info("Performing technical details search...")
                tech_results = await search_orchestrator.comprehensive_plant_details_search(plant_name)
                all_search_results.update(tech_results)

        # Log search statistics
        total_results = sum(len(results) for results in all_search_results.values())
        logger.info(f"Unified search phase completed. Found {total_results} total results across {len(all_search_results)} categories")

        for category, results in all_search_results.items():
            logger.info(f"  {category}: {len(results)} results")

        return all_search_results

    async def _scraping_phase(self, search_results: Dict) -> List[ScrapedContent]:
        """Phase 2: Scrape content from unified search results."""
        logger.info("Phase 2: Starting unified scraping phase")

        async with ScraperAPIClient(self.scraper_api_key) as scraper_client:
            scrape_orchestrator = ContentScrapeOrchestrator(scraper_client)
            scraped_contents = await scrape_orchestrator.scrape_search_results(
                search_results,
                max_pages_per_category=config.pipeline.max_scrape_pages
            )

        logger.info(f"Unified scraping phase completed. Successfully scraped {len(scraped_contents)} pages")

        # Log content statistics
        total_chars = sum(len(content.content) for content in scraped_contents)
        avg_relevance = sum(content.relevance_score for content in scraped_contents) / len(scraped_contents) if scraped_contents else 0

        logger.info(f"  Total content: {total_chars:,} characters")
        logger.info(f"  Average relevance score: {avg_relevance:.2f}")

        return scraped_contents

    async def _parallel_extraction_phase(
        self, 
        scraped_contents: List[ScrapedContent], 
        plant_name: str
    ) -> Tuple[OrganizationalDetails, PlantDetails]:
        """Phase 3: Extract both organizational and technical details in parallel."""
        logger.info("Phase 3: Starting parallel extraction phase")

        # Run both extractions in parallel
        org_task = self._organizational_extraction_phase(scraped_contents, plant_name)
        tech_task = self._technical_extraction_phase(scraped_contents, plant_name)

        org_details, plant_details = await asyncio.gather(org_task, tech_task)

        logger.info("Parallel extraction phase completed successfully")
        return org_details, plant_details

    async def _organizational_extraction_phase(
        self, 
        scraped_contents: List[ScrapedContent], 
        plant_name: str
    ) -> OrganizationalDetails:
        """Extract organizational details."""
        logger.info("Starting organizational extraction")

        if not scraped_contents:
            logger.warning("No scraped content available for organizational extraction")
            return OrganizationalDetails()

        try:
            # Use adaptive extractor for organizational details
            adaptive_extractor = AdaptiveExtractor(self.groq_api_key)
            org_details = await adaptive_extractor.extract_adaptively(scraped_contents, plant_name)

            logger.info("Organizational extraction completed successfully")
            return org_details

        except Exception as e:
            logger.error(f"Organizational extraction failed: {e}")
            return OrganizationalDetails()

    async def _technical_extraction_phase(
        self, 
        scraped_contents: List[ScrapedContent], 
        plant_name: str
    ) -> PlantDetails:
        """Extract technical plant details."""
        logger.info("Starting technical extraction")

        if not scraped_contents:
            logger.warning("No scraped content available for technical extraction")
            return PlantDetails()

        try:
            # Use plant details extractor
            groq_client = GroqExtractionClient(self.groq_api_key)
            plant_extractor = PlantDetailsExtractor(groq_client)
            
            extracted_data = await plant_extractor.extract_all_plant_details(scraped_contents, plant_name)
            plant_details = PlantDetails(**extracted_data)

            logger.info("Technical extraction completed successfully")
            return plant_details

        except Exception as e:
            logger.error(f"Technical extraction failed: {e}")
            return PlantDetails()

    async def save_results(
        self, 
        org_details: Optional[OrganizationalDetails] = None,
        plant_details: Optional[PlantDetails] = None,
        org_output_path: str = "org_details.json",
        plant_output_path: str = "plant_details_output.json"
    ):
        """Save extraction results to JSON files."""
        try:
            # Save organizational details
            if org_details:
                org_dict = org_details.model_dump()
                with open(org_output_path, 'w', encoding='utf-8') as f:
                    json.dump(org_dict, f, indent=4, ensure_ascii=False)
                logger.info(f"Organizational results saved to {org_output_path}")

            # Save plant details
            if plant_details:
                plant_dict = plant_details.model_dump()
                with open(plant_output_path, 'w', encoding='utf-8') as f:
                    json.dump(plant_dict, f, indent=4, ensure_ascii=False)
                logger.info(f"Plant details results saved to {plant_output_path}")

        except Exception as e:
            logger.error(f"Failed to save results: {e}")
            raise


async def main():
    """Main function for testing the unified pipeline."""
    import sys

    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Get plant name from command line or use default
    if len(sys.argv) > 1:
        plant_name = " ".join(sys.argv[1:])
    else:
        plant_name = "Vogtle Nuclear Power Plant"

    try:
        # Initialize unified pipeline
        pipeline = UnifiedPowerPlantPipeline()

        # Extract both organizational and technical data
        org_details, plant_details = await pipeline.extract_complete_plant_data(
            plant_name,
            extract_organizational=True,
            extract_technical=True
        )

        # Save results
        await pipeline.save_results(org_details, plant_details)

        # Print results
        print("\n" + "="*60)
        print("UNIFIED EXTRACTION RESULTS")
        print("="*60)
        
        if org_details:
            print("\nORGANIZATIONAL DETAILS:")
            print("-" * 30)
            print(json.dumps(org_details.model_dump(), indent=2))
        
        if plant_details:
            print("\nPLANT TECHNICAL DETAILS:")
            print("-" * 30)
            print(json.dumps(plant_details.model_dump(), indent=2))

    except Exception as e:
        logger.error(f"Unified pipeline execution failed: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
