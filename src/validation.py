"""
Enhanced data validation and cross-source verification for power plant data.
"""
import logging
from typing import Dict, List, Optional, Any, Tuple
from collections import Counter
import re
from difflib import SequenceMatcher

from src.models import OrganizationalDetails, ExtractionResult
from src.config import config

logger = logging.getLogger(__name__)


class DataValidator:
    """Validates and cross-references extracted power plant data."""

    def __init__(self):
        self.confidence_threshold = config.pipeline.confidence_threshold

        # Known data mappings for validation
        self.country_mappings = {
            "usa": "United States",
            "us": "United States",
            "america": "United States",
            "uk": "United Kingdom",
            "britain": "United Kingdom",
            "england": "United Kingdom"
        }

        self.plant_type_mappings = {
            "coal-fired": "coal",
            "gas-fired": "gas",
            "natural gas": "gas",
            "ccgt": "gas",
            "combined cycle": "gas",
            "hydroelectric": "hydro",
            "hydropower": "hydro",
            "photovoltaic": "solar",
            "pv": "solar",
            "wind farm": "wind",
            "atomic": "nuclear"
        }

        self.currency_by_country = {
            "United States": "USD",
            "United Kingdom": "GBP",
            "Canada": "CAD",
            "Australia": "AUD",
            "Germany": "EUR",
            "France": "EUR",
            "Spain": "EUR",
            "Italy": "EUR",
            "Japan": "JPY",
            "China": "CNY",
            "India": "INR",
            "Brazil": "BRL",
            "Mexico": "MXN"
        }

        # Fiscal year patterns by country (MM-MM format)
        self.fiscal_year_by_country = {
            "United States": "01-12",  # January to December
            "United Kingdom": "04-03",  # April to March
            "Canada": "04-03",  # April to March
            "Australia": "07-06",  # July to June
            "Germany": "01-12",  # January to December
            "France": "01-12",  # January to December
            "Spain": "01-12",  # January to December
            "Italy": "01-12",  # January to December
            "Japan": "04-03",  # April to March
            "China": "01-12",  # January to December
            "India": "04-03",  # April to March
            "Brazil": "01-12",  # January to December
            "Mexico": "01-12",  # January to December
            "Switzerland": "01-12",  # January to December
            "Netherlands": "01-12",  # January to December
            "Sweden": "01-12",  # January to December
            "Norway": "01-12",  # January to December
        }

    def validate_extraction_results(
        self,
        extraction_results: List[ExtractionResult],
        plant_name: str
    ) -> Dict[str, Any]:
        """
        Validate and consolidate extraction results from multiple sources.

        Args:
            extraction_results: List of extraction results for all fields
            plant_name: Name of the power plant

        Returns:
            Dictionary with validated and consolidated data
        """
        logger.info(f"Starting validation for {len(extraction_results)} extraction results")

        # Group results by field
        results_by_field = {}
        for result in extraction_results:
            field_name = result.field_name
            if field_name not in results_by_field:
                results_by_field[field_name] = []
            results_by_field[field_name].append(result)

        validated_data = {}
        validation_summary = {}

        # Validate each field
        for field_name in ["cfpp_type", "organization_name", "country_name", "province",
                          "plants_count", "plant_types", "ppa_flag", "currency_in", "financial_year"]:

            field_results = results_by_field.get(field_name, [])
            validated_value, confidence, method = self._validate_field(
                field_name, field_results, plant_name, validated_data
            )

            validated_data[field_name] = validated_value
            validation_summary[field_name] = {
                "confidence": confidence,
                "method": method,
                "source_count": len(field_results)
            }

        logger.info(f"Validation completed. Summary: {self._summarize_validation(validation_summary)}")

        return {
            "data": validated_data,
            "validation_summary": validation_summary
        }

    def _validate_field(
        self,
        field_name: str,
        results: List[ExtractionResult],
        plant_name: str,
        existing_data: Dict[str, Any]
    ) -> Tuple[Any, float, str]:
        """Validate a specific field using multiple strategies."""

        if not results:
            return self._get_fallback_value(field_name, plant_name, existing_data)

        # Filter results by confidence threshold
        high_confidence_results = [r for r in results if r.confidence_score >= self.confidence_threshold]

        if high_confidence_results:
            # Use consensus from high-confidence results
            return self._get_consensus_value(field_name, high_confidence_results)
        else:
            # Try fallback strategies
            return self._get_fallback_value(field_name, plant_name, existing_data)

    def _get_consensus_value(self, field_name: str, results: List[ExtractionResult]) -> Tuple[Any, float, str]:
        """Get consensus value from multiple high-confidence results."""

        if len(results) == 1:
            result = results[0]
            validated_value = self._normalize_value(field_name, result.extracted_value)
            return validated_value, result.confidence_score, "single_source"

        # Multiple results - find consensus
        values = [self._normalize_value(field_name, r.extracted_value) for r in results]

        if field_name in ["cfpp_type", "organization_name", "country_name", "province", "currency_in", "financial_year"]:
            # String fields - use most common value
            return self._get_string_consensus(values, results)

        elif field_name == "plants_count":
            # Numeric field - use median or most common
            return self._get_numeric_consensus(values, results)

        elif field_name == "plant_types":
            # List field - merge unique values
            return self._get_list_consensus(values, results)

        elif field_name == "ppa_flag":
            # Boolean field - use majority vote
            return self._get_boolean_consensus(values, results)

        else:
            # Default to first high-confidence result
            result = max(results, key=lambda r: r.confidence_score)
            validated_value = self._normalize_value(field_name, result.extracted_value)
            return validated_value, result.confidence_score, "highest_confidence"

    def _get_string_consensus(self, values: List[str], results: List[ExtractionResult]) -> Tuple[str, float, str]:
        """Get consensus for string fields."""
        # Remove None/empty values
        valid_values = [v for v in values if v and v.strip()]

        if not valid_values:
            return "", 0.1, "no_valid_values"

        # Count occurrences
        value_counts = Counter(valid_values)
        most_common_value, count = value_counts.most_common(1)[0]

        # Calculate consensus confidence
        consensus_ratio = count / len(valid_values)
        avg_confidence = sum(r.confidence_score for r in results) / len(results)
        final_confidence = avg_confidence * consensus_ratio

        return most_common_value, final_confidence, f"consensus_{count}of{len(valid_values)}"

    def _get_numeric_consensus(self, values: List[int], results: List[ExtractionResult]) -> Tuple[Optional[int], float, str]:
        """Get consensus for numeric fields."""
        valid_values = [v for v in values if v is not None]

        if not valid_values:
            return None, 0.1, "no_valid_values"

        # Use median for numeric consensus
        sorted_values = sorted(valid_values)
        median_value = sorted_values[len(sorted_values) // 2]

        # Calculate confidence based on value spread
        if len(set(valid_values)) == 1:
            # All values are the same
            avg_confidence = sum(r.confidence_score for r in results) / len(results)
            return median_value, avg_confidence, "unanimous"
        else:
            # Values differ - lower confidence
            avg_confidence = sum(r.confidence_score for r in results) / len(results)
            return median_value, avg_confidence * 0.7, "median_consensus"

    def _get_list_consensus(self, values: List[List[str]], results: List[ExtractionResult]) -> Tuple[List[str], float, str]:
        """Get consensus for list fields."""
        all_items = []
        for value_list in values:
            if value_list:
                all_items.extend(value_list)

        if not all_items:
            return [], 0.1, "no_valid_values"

        # Count item occurrences and keep items mentioned multiple times
        item_counts = Counter(all_items)
        consensus_items = [item for item, count in item_counts.items() if count >= 2 or len(values) == 1]

        # If no consensus, take unique items from highest confidence result
        if not consensus_items and results:
            best_result = max(results, key=lambda r: r.confidence_score)
            consensus_items = best_result.extracted_value or []

        avg_confidence = sum(r.confidence_score for r in results) / len(results)
        return consensus_items, avg_confidence, "merged_consensus"

    def _get_boolean_consensus(self, values: List[bool], results: List[ExtractionResult]) -> Tuple[Optional[bool], float, str]:
        """Get consensus for boolean fields."""
        valid_values = [v for v in values if v is not None]

        if not valid_values:
            return None, 0.1, "no_valid_values"

        # Majority vote
        true_count = sum(1 for v in valid_values if v)
        false_count = len(valid_values) - true_count

        if true_count > false_count:
            consensus_value = True
            confidence_ratio = true_count / len(valid_values)
        elif false_count > true_count:
            consensus_value = False
            confidence_ratio = false_count / len(valid_values)
        else:
            # Tie - use highest confidence result
            best_result = max(results, key=lambda r: r.confidence_score)
            return best_result.extracted_value, best_result.confidence_score, "tie_breaker"

        avg_confidence = sum(r.confidence_score for r in results) / len(results)
        final_confidence = avg_confidence * confidence_ratio

        return consensus_value, final_confidence, f"majority_{int(confidence_ratio*100)}pct"

    def _normalize_value(self, field_name: str, value: Any) -> Any:
        """Normalize values for consistency."""
        if value is None:
            return None

        if field_name == "cfpp_type":
            return self._normalize_plant_type(str(value))
        elif field_name == "country_name":
            return self._normalize_country_name(str(value))
        elif field_name == "currency_in":
            return str(value).upper() if value else ""
        elif field_name in ["organization_name", "province", "financial_year"]:
            return str(value).strip() if value else ""
        else:
            return value

    def _normalize_plant_type(self, plant_type: str) -> str:
        """Normalize plant type values."""
        if not plant_type:
            return ""

        plant_type_lower = plant_type.lower().strip()

        # Check mappings
        for key, normalized in self.plant_type_mappings.items():
            if key in plant_type_lower:
                return normalized

        return plant_type_lower

    def _normalize_country_name(self, country: str) -> str:
        """Normalize country names."""
        if not country:
            return ""

        country_lower = country.lower().strip()

        # Check mappings
        if country_lower in self.country_mappings:
            return self.country_mappings[country_lower]

        return country.title().strip()

    def _get_fallback_value(
        self,
        field_name: str,
        plant_name: str,
        existing_data: Dict[str, Any]
    ) -> Tuple[Any, float, str]:
        """Get fallback value when no high-confidence extractions available."""

        # Try inference from other fields
        if field_name == "currency_in" and existing_data.get("country_name"):
            country = existing_data["country_name"]
            if country in self.currency_by_country:
                return self.currency_by_country[country], 0.6, "country_inference"

        if field_name == "financial_year" and existing_data.get("country_name"):
            country = existing_data["country_name"]
            if country in self.fiscal_year_by_country:
                return self.fiscal_year_by_country[country], 0.6, "country_inference"

        # Try plant name analysis
        if field_name == "country_name":
            inferred_country = self._infer_country_from_name(plant_name)
            if inferred_country:
                return inferred_country, 0.4, "name_inference"

        # Default fallback values
        fallback_values = {
            "cfpp_type": ("", 0.0, "no_data"),
            "organization_name": ("", 0.0, "no_data"),
            "country_name": ("", 0.0, "no_data"),
            "province": ("", 0.0, "no_data"),
            "plants_count": (None, 0.0, "no_data"),
            "plant_types": ([], 0.0, "no_data"),
            "ppa_flag": ("", 0.0, "no_data"),
            "currency_in": ("", 0.0, "no_data"),
            "financial_year": ("", 0.0, "no_data")
        }

        return fallback_values.get(field_name, ("", 0.0, "no_data"))

    def _infer_country_from_name(self, plant_name: str) -> Optional[str]:
        """Try to infer country from plant name."""
        name_lower = plant_name.lower()

        # Common patterns
        if any(term in name_lower for term in ["usa", "america", "us "]):
            return "United States"
        elif any(term in name_lower for term in ["uk ", "britain", "england"]):
            return "United Kingdom"
        elif "canada" in name_lower:
            return "Canada"
        elif "australia" in name_lower:
            return "Australia"

        return None

    def _summarize_validation(self, validation_summary: Dict) -> str:
        """Create summary of validation results."""
        high_confidence_fields = sum(1 for v in validation_summary.values() if v["confidence"] >= 0.7)
        total_fields = len(validation_summary)

        return f"{high_confidence_fields}/{total_fields} high-confidence fields"


class CrossSourceValidator:
    """Validates data across multiple sources and extraction attempts."""

    def __init__(self):
        self.validator = DataValidator()

    async def validate_multi_source_extraction(
        self,
        extraction_attempts: List[Dict[str, Any]],
        plant_name: str
    ) -> OrganizationalDetails:
        """
        Validate extraction results from multiple attempts/sources.

        Args:
            extraction_attempts: List of extraction result dictionaries
            plant_name: Name of the power plant

        Returns:
            Validated OrganizationalDetails object
        """
        logger.info(f"Cross-source validation for {len(extraction_attempts)} extraction attempts")

        # Flatten all extraction results
        all_results = []
        for attempt in extraction_attempts:
            if "extraction_results" in attempt:
                all_results.extend(attempt["extraction_results"])

        if not all_results:
            logger.warning("No extraction results available for validation")
            return OrganizationalDetails()

        # Validate using consensus approach
        validation_result = self.validator.validate_extraction_results(all_results, plant_name)

        validated_data = validation_result["data"]
        validation_summary = validation_result["validation_summary"]

        # Log validation summary
        logger.info(f"Validation summary: {validation_summary}")

        try:
            # Create validated OrganizationalDetails object
            org_details = OrganizationalDetails(**validated_data)

            # Add validation metadata (if needed for debugging)
            org_details.__dict__["_validation_summary"] = validation_summary

            return org_details

        except Exception as e:
            logger.error(f"Failed to create validated OrganizationalDetails: {e}")
            return OrganizationalDetails()
