"""
OpenAI client for power plant data extraction.
"""
import asyncio
import logging
from typing import Dict, List, Optional, Union, Any
import json
import re
from openai import OpenAI
from tenacity import retry, stop_after_attempt, wait_exponential

from src.models import ScrapedContent, ExtractionResult
from src.config import config

logger = logging.getLogger(__name__)


class OpenAIExtractionClient:
    """Client for extracting structured data using OpenAI LLM."""

    def __init__(self, api_key: str, model: str = "gpt-4o-mini"):
        self.client = OpenAI(api_key=api_key)
        self.model = model
        self.is_async = False
        logger.info(f"Initialized OpenAI client with model: {model}")

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    async def extract_field(
        self,
        field_name: str,
        content: str,
        plant_name: str = None,
        organization_name: str = None
    ) -> ExtractionResult:
        """
        Extract a specific field from content using OpenAI.

        Args:
            field_name: Name of the field to extract
            content: Content to extract from
            plant_name: Plant name for context
            organization_name: Organization name for context

        Returns:
            ExtractionResult with extracted value and confidence
        """
        # Handle direct prompt case (for compatibility with bedrock interface)
        if isinstance(field_name, str) and content is None and plant_name is None:
            prompt = field_name  # field_name is actually the prompt
            raw_result = await self._extract_with_prompt(prompt)

            # Return as ExtractionResult for compatibility
            return ExtractionResult(
                field_name="direct_prompt",
                extracted_value=raw_result or "unknown",
                confidence_score=0.7 if raw_result else 0.0,
                source_url="",
                extraction_method="openai_direct_prompt",
                source_type="html"
            )

        # Get the appropriate prompt template
        prompt_templates = config.extraction_prompts
        if field_name not in prompt_templates:
            logger.warning(f"No prompt template found for field: {field_name}")
            return ExtractionResult(
                field_name=field_name,
                extracted_value="unknown",
                confidence_score=0.0,
                source_url="",
                extraction_method="openai_no_template",
                source_type="unknown"
            )

        # Format the prompt
        prompt_template = prompt_templates[field_name]
        prompt = prompt_template.format(
            plant_name=plant_name or "Unknown Plant",
            organization_name=organization_name or "Unknown Organization",
            content=content[:4000]  # Limit content length for OpenAI
        )

        try:
            # Use sync client in executor to avoid async issues
            response = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.client.chat.completions.create(
                    model=self.model,
                    messages=[
                        {
                            "role": "system",
                            "content": "You are an expert at extracting structured information about power plants from web content. Always return concise, accurate answers based only on the provided content."
                        },
                        {
                            "role": "user",
                            "content": prompt
                        }
                    ],
                    temperature=0.1,
                    max_tokens=100  # Reduced to save tokens
                )
            )

            extracted_value = response.choices[0].message.content.strip()

            # Process field-specific responses
            processed_value, confidence = self._process_field_response(extracted_value, field_name)

            logger.debug(f"OpenAI extracted {field_name}: {processed_value} (confidence: {confidence:.2f})")

            return ExtractionResult(
                field_name=field_name,
                extracted_value=processed_value,
                confidence_score=confidence,
                source_url="",
                extraction_method="openai_llm",
                source_type="html"
            )

        except Exception as e:
            logger.error(f"OpenAI extraction failed for {field_name}: {e}")
            return ExtractionResult(
                field_name=field_name,
                extracted_value="unknown",
                confidence_score=0.0,
                source_url="",
                extraction_method="openai_error",
                source_type="unknown"
            )

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    async def _extract_with_prompt(self, prompt: str) -> Optional[str]:
        """
        Internal method to extract using a formatted prompt.
        Used for compatibility with other LLM interfaces.

        Args:
            prompt: Extraction prompt

        Returns:
            Extracted field value or None
        """
        try:
            logger.debug(f"OpenAI extraction prompt length: {len(prompt)}")

            # Use sync client in executor
            response = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.client.chat.completions.create(
                    model=self.model,
                    messages=[
                        {
                            "role": "system",
                            "content": "You are an expert at extracting technical information about power plants from documents. Be precise and only extract information that is explicitly stated."
                        },
                        {
                            "role": "user",
                            "content": prompt
                        }
                    ],
                    temperature=0.1,
                    max_tokens=1000
                )
            )

            result = response.choices[0].message.content.strip()

            if result:
                logger.debug(f"OpenAI extraction response length: {len(result)}")
                return result
            else:
                logger.warning("Empty response from OpenAI")
                return None

        except Exception as e:
            logger.error(f"OpenAI extraction failed: {e}")
            return None

    def _process_field_response(self, raw_response: str, field_name: str) -> tuple:
        """Process field-specific responses and return (value, confidence)."""
        if not raw_response or raw_response.lower() in ["unknown", "not found", "", "n/a"]:
            return self._get_default_value_for_field(field_name), 0.0

        response_lower = raw_response.lower().strip()

        # Field-specific processing (similar to bedrock_client.py)
        if field_name == "cfpp_type":
            # Check for ownership types
            if any(term in response_lower for term in ["private", "privately owned"]):
                return "private", 0.8
            elif any(term in response_lower for term in ["public", "government", "state"]):
                return "public", 0.8
            elif any(term in response_lower for term in ["joint venture", "partnership"]):
                return "joint_venture", 0.8
            else:
                return raw_response.strip(), 0.6

        elif field_name == "plants_count":
            # Extract number
            numbers = re.findall(r'\d+', raw_response)
            if numbers:
                try:
                    count = int(numbers[0])
                    if 1 <= count <= 1000:
                        return count, 0.8
                except ValueError:
                    pass
            return None, 0.1

        elif field_name == "plant_types":
            # Split by common delimiters and return as list
            types_raw = re.split(r'[,;|\n]', response_lower)
            valid_types = []
            known_types = ["coal", "gas", "nuclear", "solar", "wind", "hydro", "biomass", "geothermal", "oil"]

            for type_raw in types_raw:
                type_clean = type_raw.strip()
                if type_clean in known_types:
                    valid_types.append(type_clean)

            if valid_types:
                return valid_types, 0.8
            return [], 0.1

        else:
            # Default processing with confidence calculation
            confidence = self._calculate_confidence(raw_response, field_name)
            return raw_response.strip(), confidence

    def _calculate_confidence(self, extracted_value: str, field_name: str) -> float:
        """Calculate confidence score based on extracted value quality."""
        if not extracted_value or extracted_value.lower() in ["unknown", "not found", "", "n/a"]:
            return 0.0

        # Base confidence
        confidence = 0.7

        # Adjust based on field-specific patterns
        if field_name == "organization_name":
            if any(word in extracted_value.lower() for word in ["limited", "ltd", "inc", "corp", "company"]):
                confidence += 0.2
        elif field_name == "country_name":
            if len(extracted_value.split()) <= 3:  # Country names are usually short
                confidence += 0.2
        elif field_name == "province":
            if len(extracted_value.split()) <= 3:  # Province names are usually short
                confidence += 0.2
        elif field_name in ["lat", "long"]:
            try:
                float(extracted_value)
                confidence += 0.3  # High confidence for valid coordinates
            except ValueError:
                confidence = 0.1  # Low confidence for invalid coordinates

        return min(confidence, 1.0)

    def _get_default_value_for_field(self, field_name: str) -> Union[str, int, List]:
        """Get default value for a specific field."""
        if field_name in ["plants_count"]:
            return None
        elif field_name in ["plant_types"]:
            return []
        else:
            return ""


class PowerPlantDataExtractor:
    """Main extractor class compatible with existing pipeline."""

    def __init__(self, openai_client: OpenAIExtractionClient):
        self.groq_client = openai_client  # For compatibility
        self.openai_client = openai_client

    async def extract_organizational_details(
        self,
        scraped_contents: List[ScrapedContent],
        plant_name: str
    ) -> Dict[str, Any]:
        """
        Extract organizational details from scraped content.

        Args:
            scraped_contents: List of scraped web content
            plant_name: Name of the power plant

        Returns:
            Dictionary with extracted organizational details
        """
        logger.info(f"Starting OpenAI LLM extraction for {len(scraped_contents)} content pieces")

        # Combine all content
        combined_content = "\n\n".join([
            f"Source: {content.url}\nTitle: {content.title}\nContent: {content.content}"
            for content in scraped_contents
        ])

        # Fields to extract
        fields_to_extract = [
            "organization_name", "cfpp_type", "country_name", "province",
            "plants_count", "plant_types", "ppa_flag", "currency_in", "financial_year"
        ]

        extracted_data = {}
        organization_name = None

        # Extract organization name first
        try:
            result = await self.openai_client.extract_field(
                "organization_name", combined_content, plant_name
            )
            if result.confidence_score >= config.pipeline.confidence_threshold:
                organization_name = result.extracted_value
                extracted_data["organization_name"] = organization_name
            else:
                extracted_data["organization_name"] = "unknown"

            logger.info(f"Extracted organization_name: {organization_name} (confidence: {result.confidence_score:.2f})")
            await asyncio.sleep(0.5)

        except Exception as e:
            logger.error(f"Failed to extract organization_name: {e}")
            extracted_data["organization_name"] = "unknown"

        # Extract other fields
        for field_name in fields_to_extract:
            if field_name == "organization_name":
                continue  # Already extracted

            try:
                result = await self.openai_client.extract_field(
                    field_name, combined_content, plant_name, organization_name
                )

                if result.confidence_score >= config.pipeline.confidence_threshold:
                    extracted_data[field_name] = result.extracted_value
                else:
                    extracted_data[field_name] = self._get_default_value(field_name)

                logger.info(f"Extracted {field_name}: {result.extracted_value} (confidence: {result.confidence_score:.2f})")

                # Small delay between extractions
                await asyncio.sleep(0.5)

            except Exception as e:
                logger.error(f"Failed to extract {field_name}: {e}")
                extracted_data[field_name] = self._get_default_value(field_name)

        return extracted_data

    async def extract_all_fields(
        self,
        scraped_contents: List[ScrapedContent],
        plant_name: str
    ) -> Dict[str, Any]:
        """
        Extract all organizational fields from scraped content.
        This method provides compatibility with the existing pipeline.

        Args:
            scraped_contents: List of scraped web content
            plant_name: Name of the power plant

        Returns:
            Dictionary with extracted organizational details
        """
        return await self.extract_organizational_details(scraped_contents, plant_name)

    def _get_default_value(self, field_name: str) -> Union[str, int, List]:
        """Get default value for a field."""
        if field_name in ["plants_count"]:
            return None
        elif field_name in ["plant_types"]:
            return []
        else:
            return ""
