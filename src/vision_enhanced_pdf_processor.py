"""
Vision-Enhanced PDF Processor that integrates with existing scraper infrastructure.
Extends the current PDF processing to support vision-based extraction for scanned documents.
"""

import logging
import io
from typing import Optional, Tuple, Dict, Any
import fitz  # PyMuPDF
from pdf2image import convert_from_bytes
from PIL import Image

from src.pdf_processor import PDFProcessor
from src.vision_enhanced_openai_client import VisionEnhancedOpenAIClient, VisionExtractionResult

logger = logging.getLogger(__name__)

class VisionEnhancedPDFProcessor(PDFProcessor):
    """
    Enhanced PDF processor that combines traditional text extraction with vision capabilities.
    Automatically detects document type and chooses optimal extraction strategy.
    """
    
    def __init__(self, vision_client: VisionEnhancedOpenAIClient = None):
        """
        Initialize vision-enhanced PDF processor.
        
        Args:
            vision_client: Optional vision-enhanced OpenAI client for image processing
        """
        super().__init__()
        self.vision_client = vision_client
        self.vision_enabled = vision_client is not None
        
        # Enhanced processing settings
        self.min_text_ratio = 0.1  # Minimum text-to-page ratio for text-based classification
        self.max_vision_pages = 5  # Maximum pages to process with vision
        
        logger.info(f"Vision-enhanced PDF processor initialized (vision_enabled: {self.vision_enabled})")

    def extract_text_and_metadata_enhanced(self, pdf_bytes: bytes, url: str = "") -> Tuple[Optional[str], Optional[str], Dict[str, Any]]:
        """
        Enhanced extraction that returns text, title, and document metadata.
        
        Args:
            pdf_bytes: Raw PDF bytes
            url: Source URL for logging
            
        Returns:
            Tuple of (extracted_text, title, metadata_dict)
        """
        if not pdf_bytes:
            logger.warning(f"Empty PDF bytes for {url}")
            return None, None, {}
        
        # Initialize metadata
        metadata = {
            "document_type": "unknown",
            "total_pages": 0,
            "text_extraction_success": False,
            "vision_capable": self.vision_enabled,
            "processing_method": "text_only"
        }
        
        try:
            # Get basic PDF info
            doc = fitz.open(stream=pdf_bytes, filetype="pdf")
            metadata["total_pages"] = len(doc)
            doc.close()
            
            # Try traditional text extraction first
            text, title = self.extract_text_from_pdf_bytes(pdf_bytes, url)
            
            if text and len(text) >= self.min_text_length:
                # Successful text extraction
                metadata["text_extraction_success"] = True
                metadata["document_type"] = self._classify_document_type(text, pdf_bytes)
                metadata["processing_method"] = "text_extraction"
                
                logger.info(f"Text extraction successful for {url}: {len(text)} chars, type: {metadata['document_type']}")
                return text, title, metadata
            
            else:
                # Text extraction failed or insufficient - document might be scanned
                metadata["text_extraction_success"] = False
                metadata["document_type"] = "scanned_or_image_based"
                
                if self.vision_enabled:
                    logger.info(f"Text extraction insufficient for {url}, document appears to be scanned")
                    metadata["processing_method"] = "vision_ready"
                else:
                    logger.warning(f"Text extraction failed and vision not available for {url}")
                    metadata["processing_method"] = "failed"
                
                return text, title, metadata
                
        except Exception as e:
            logger.error(f"Enhanced PDF processing failed for {url}: {e}")
            metadata["processing_method"] = "error"
            return None, None, metadata

    def _classify_document_type(self, text_content: str, pdf_bytes: bytes) -> str:
        """
        Classify document type based on text content and structure.
        
        Args:
            text_content: Extracted text content
            pdf_bytes: Raw PDF bytes for additional analysis
            
        Returns:
            Document type classification
        """
        try:
            text_length = len(text_content.strip())
            word_count = len(text_content.split())
            
            # Calculate text density
            doc = fitz.open(stream=pdf_bytes, filetype="pdf")
            total_pages = len(doc)
            doc.close()
            
            text_per_page = text_length / max(total_pages, 1)
            
            # Classification logic
            if text_length > 5000 and word_count > 800 and text_per_page > 500:
                return "text_based_rich"
            elif text_length > 1000 and word_count > 200 and text_per_page > 200:
                return "text_based_standard"
            elif text_length > 100 and word_count > 20:
                return "hybrid_text_image"
            elif text_length < 100:
                return "scanned_or_image_based"
            else:
                return "text_based_sparse"
                
        except Exception as e:
            logger.warning(f"Document classification failed: {e}")
            return "unknown"

    async def extract_field_with_vision_fallback(self, field_name: str, pdf_bytes: bytes, context: str = "", url: str = "") -> Optional[VisionExtractionResult]:
        """
        Extract specific field using hybrid text + vision approach.
        
        Args:
            field_name: Name of the field to extract
            pdf_bytes: Raw PDF bytes
            context: Additional context for extraction
            url: Source URL for logging
            
        Returns:
            VisionExtractionResult with extracted value and metadata
        """
        if not self.vision_enabled:
            logger.warning("Vision extraction requested but vision client not available")
            return None
        
        try:
            # First, get text content and metadata
            text_content, title, metadata = self.extract_text_and_metadata_enhanced(pdf_bytes, url)
            
            # Use hybrid extraction strategy
            result = await self.vision_client.extract_field_hybrid(
                field_name=field_name,
                pdf_bytes=pdf_bytes,
                text_content=text_content,
                context=context
            )
            
            if result:
                # Enhance result with PDF metadata
                result.source_info += f" | PDF Type: {metadata.get('document_type', 'unknown')}"
                result.pages_processed = metadata.get('total_pages', 0)
                
                logger.info(f"Hybrid extraction completed for {field_name} from {url}: {result.extraction_method}")
            
            return result
            
        except Exception as e:
            logger.error(f"Vision fallback extraction failed for {field_name} from {url}: {e}")
            return None

    async def extract_multiple_fields_with_vision(self, field_list: list, pdf_bytes: bytes, context: str = "", url: str = "") -> Dict[str, VisionExtractionResult]:
        """
        Extract multiple fields using optimal strategy based on document type.
        
        Args:
            field_list: List of field names to extract
            pdf_bytes: Raw PDF bytes
            context: Additional context for extraction
            url: Source URL for logging
            
        Returns:
            Dictionary mapping field names to VisionExtractionResults
        """
        if not self.vision_enabled:
            logger.warning("Vision extraction requested but vision client not available")
            return {}
        
        try:
            # Analyze document first
            text_content, title, metadata = self.extract_text_and_metadata_enhanced(pdf_bytes, url)
            document_type = metadata.get('document_type', 'unknown')
            
            logger.info(f"Processing {len(field_list)} fields from {document_type} document: {url}")
            
            results = {}
            
            # Strategy based on document type
            if document_type in ['scanned_or_image_based', 'hybrid_text_image']:
                # Use vision extraction for scanned/hybrid documents
                logger.info(f"Using vision extraction for {document_type} document")
                results = await self.vision_client.extract_multiple_fields_vision(
                    field_list=field_list,
                    pdf_bytes=pdf_bytes,
                    context=context
                )
                
            elif document_type in ['text_based_rich', 'text_based_standard'] and text_content:
                # Use text extraction for text-rich documents, with vision fallback
                logger.info(f"Using text extraction with vision fallback for {document_type} document")
                
                for field_name in field_list:
                    result = await self.vision_client.extract_field_hybrid(
                        field_name=field_name,
                        pdf_bytes=pdf_bytes,
                        text_content=text_content,
                        context=context
                    )
                    if result:
                        results[field_name] = result
                        
            else:
                # Fallback to hybrid approach
                logger.info(f"Using hybrid approach for {document_type} document")
                for field_name in field_list:
                    result = await self.vision_client.extract_field_hybrid(
                        field_name=field_name,
                        pdf_bytes=pdf_bytes,
                        text_content=text_content,
                        context=context
                    )
                    if result:
                        results[field_name] = result
            
            # Enhance all results with document metadata
            for field_name, result in results.items():
                result.source_info += f" | PDF: {document_type}, Pages: {metadata.get('total_pages', 0)}"
            
            logger.info(f"Extracted {len(results)}/{len(field_list)} fields from {url}")
            return results
            
        except Exception as e:
            logger.error(f"Multiple field vision extraction failed for {url}: {e}")
            return {}

    def is_vision_extraction_recommended(self, pdf_bytes: bytes, url: str = "") -> bool:
        """
        Determine if vision extraction is recommended for this PDF.
        
        Args:
            pdf_bytes: Raw PDF bytes
            url: Source URL for logging
            
        Returns:
            True if vision extraction is recommended
        """
        if not self.vision_enabled:
            return False
        
        try:
            text_content, title, metadata = self.extract_text_and_metadata_enhanced(pdf_bytes, url)
            document_type = metadata.get('document_type', 'unknown')
            
            # Recommend vision for scanned, image-based, or sparse text documents
            vision_recommended_types = [
                'scanned_or_image_based',
                'hybrid_text_image',
                'text_based_sparse',
                'unknown'
            ]
            
            recommended = document_type in vision_recommended_types
            logger.info(f"Vision extraction recommended for {url}: {recommended} (type: {document_type})")
            
            return recommended
            
        except Exception as e:
            logger.warning(f"Vision recommendation analysis failed for {url}: {e}")
            return True  # Default to recommending vision if analysis fails

    def get_processing_stats(self) -> Dict[str, Any]:
        """Get processing statistics and capabilities."""
        stats = {
            "vision_enabled": self.vision_enabled,
            "max_pages": self.max_pages,
            "max_vision_pages": self.max_vision_pages,
            "min_text_length": self.min_text_length,
            "min_text_ratio": self.min_text_ratio
        }
        
        if self.vision_enabled and self.vision_client:
            stats.update(self.vision_client.get_usage_stats())
        
        return stats
