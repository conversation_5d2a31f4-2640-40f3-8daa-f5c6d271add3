"""
OpenAI-powered extraction client for universal unit-level data extraction.
Uses GPT-4o for high-quality structured data extraction with tiered approach.
"""

import asyncio
import json
import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
import openai
from openai import AsyncOpenAI
import os
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ExtractionResult:
    """Result of field extraction with confidence score."""
    extracted_value: Any
    confidence_score: float
    source_info: str = ""
    extraction_method: str = "openai_gpt4o"

class OpenAIExtractionClient:
    """
    OpenAI-powered extraction client using GPT-4o for universal data extraction.
    Optimized for power plant unit-level technical specifications.
    """
    
    def __init__(self, api_key: str, model: str = "gpt-4o"):
        """
        Initialize OpenAI extraction client.
        
        Args:
            api_key: OpenAI API key
            model: OpenAI model to use (default: gpt-4o for best quality)
        """
        self.client = AsyncOpenAI(api_key=api_key)
        self.model = model
        self.extraction_count = 0
        self.total_tokens_used = 0
        
        logger.info(f"OpenAI extraction client initialized with model: {model}")

    async def extract_field(self, field_name: str, content: str, context: str = "") -> Optional[ExtractionResult]:
        """
        Extract a specific field from content using OpenAI GPT-4o.
        
        Args:
            field_name: Name of the field to extract
            content: Source content to extract from
            context: Additional context for extraction
            
        Returns:
            ExtractionResult with extracted value and confidence score
        """
        try:
            self.extraction_count += 1
            
            # Create optimized prompt for GPT-4o
            prompt = self._create_extraction_prompt(field_name, content, context)
            
            # Call OpenAI API
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system", 
                        "content": "You are an expert power plant data extraction specialist. Extract precise technical information from documents with high accuracy. Always provide confidence scores."
                    },
                    {
                        "role": "user", 
                        "content": prompt
                    }
                ],
                temperature=0.1,  # Low temperature for consistent extraction
                max_tokens=500,   # Sufficient for most field extractions
                response_format={"type": "json_object"}
            )
            
            # Track token usage
            if hasattr(response, 'usage') and response.usage:
                self.total_tokens_used += response.usage.total_tokens
            
            # Parse response
            result_text = response.choices[0].message.content
            result_json = json.loads(result_text)
            
            # Extract value and confidence
            extracted_value = result_json.get("value")
            confidence = float(result_json.get("confidence", 0.0))
            reasoning = result_json.get("reasoning", "")
            
            logger.info(f"Extracted {field_name}: {str(extracted_value)[:50]}{'...' if len(str(extracted_value)) > 50 else ''} (confidence: {confidence:.2f})")
            
            return ExtractionResult(
                extracted_value=extracted_value,
                confidence_score=confidence,
                source_info=reasoning,
                extraction_method=f"openai_{self.model}"
            )
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON parsing failed for field {field_name}: {e}")
            return None
        except Exception as e:
            logger.error(f"OpenAI extraction failed for field {field_name}: {e}")
            return None

    def _create_extraction_prompt(self, field_name: str, content: str, context: str) -> str:
        """Create optimized extraction prompt for GPT-4o."""
        
        # Field-specific extraction instructions
        field_instructions = {
            "capacity": "Extract the unit capacity in MW. Look for specific unit capacity, not total plant capacity.",
            "capacity_unit": "Extract the unit of measurement for capacity (usually MW).",
            "technology": "Extract the power generation technology (e.g., Supercritical, Subcritical, Ultra Supercritical).",
            "fuel_type": "Extract the primary fuel type and create a structured list with fuel details.",
            "heat_rate": "Extract the station heat rate or unit heat rate in kcal/kWh or BTU/kWh.",
            "unit_efficiency": "Extract the thermal efficiency percentage of the unit.",
            "commencement_date": "Extract the commercial operation date (COD) or commissioning date.",
            "boiler_type": "Extract the boiler technology type (e.g., Pulverized Coal, Circulating Fluidized Bed).",
            "plf": "Extract the Plant Load Factor (PLF) or Capacity Utilization Factor (CUF) as percentage.",
            "emission_factor": "Extract CO2 emission factor in kg CO2/MWh or similar units."
        }
        
        instruction = field_instructions.get(field_name, f"Extract the value for {field_name} from the content.")
        
        # Truncate content to avoid token limits
        max_content_length = 8000  # Leave room for prompt and response
        if len(content) > max_content_length:
            content = content[:max_content_length] + "... [content truncated]"
        
        prompt = f"""
Extract the field "{field_name}" from the following power plant technical content.

FIELD: {field_name}
INSTRUCTION: {instruction}
CONTEXT: {context}

CONTENT:
{content}

EXTRACTION RULES:
1. Extract ONLY the specific value requested
2. For numerical values, extract just the number without units unless units are part of the field
3. For dates, use ISO format (YYYY-MM-DD) when possible
4. For lists/arrays, structure them properly as JSON arrays
5. If the exact value is not found, return null
6. Provide a confidence score from 0.0 to 1.0 based on how certain you are

RESPONSE FORMAT (JSON):
{{
    "value": <extracted_value_or_null>,
    "confidence": <confidence_score_0_to_1>,
    "reasoning": "<brief_explanation_of_extraction>"
}}

Extract the value now:
"""
        return prompt

    async def extract_multiple_fields(self, field_list: List[str], content: str, context: str = "") -> Dict[str, ExtractionResult]:
        """
        Extract multiple fields in a single API call for efficiency.
        
        Args:
            field_list: List of field names to extract
            content: Source content
            context: Additional context
            
        Returns:
            Dictionary mapping field names to ExtractionResults
        """
        try:
            # Create batch extraction prompt
            prompt = self._create_batch_extraction_prompt(field_list, content, context)
            
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert power plant data extraction specialist. Extract multiple technical fields simultaneously with high accuracy."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.1,
                max_tokens=1500,  # More tokens for multiple fields
                response_format={"type": "json_object"}
            )
            
            # Track token usage
            if hasattr(response, 'usage') and response.usage:
                self.total_tokens_used += response.usage.total_tokens
            
            # Parse response
            result_text = response.choices[0].message.content
            result_json = json.loads(result_text)
            
            # Convert to ExtractionResult objects
            results = {}
            for field_name in field_list:
                field_data = result_json.get(field_name, {})
                if field_data:
                    results[field_name] = ExtractionResult(
                        extracted_value=field_data.get("value"),
                        confidence_score=float(field_data.get("confidence", 0.0)),
                        source_info=field_data.get("reasoning", ""),
                        extraction_method=f"openai_{self.model}_batch"
                    )
            
            logger.info(f"Batch extracted {len(results)} fields successfully")
            return results
            
        except Exception as e:
            logger.error(f"Batch extraction failed: {e}")
            return {}

    def _create_batch_extraction_prompt(self, field_list: List[str], content: str, context: str) -> str:
        """Create prompt for extracting multiple fields at once."""
        
        # Truncate content
        max_content_length = 6000
        if len(content) > max_content_length:
            content = content[:max_content_length] + "... [content truncated]"
        
        fields_json = {field: f"Extract {field} value" for field in field_list}
        
        prompt = f"""
Extract the following fields from the power plant technical content:

FIELDS TO EXTRACT: {field_list}
CONTEXT: {context}

CONTENT:
{content}

EXTRACTION RULES:
1. Extract each field value precisely
2. Return null if field value is not found
3. Provide confidence score (0.0-1.0) for each field
4. Structure numerical values appropriately

RESPONSE FORMAT (JSON):
{{
    "field_name": {{
        "value": <extracted_value_or_null>,
        "confidence": <confidence_score>,
        "reasoning": "<brief_explanation>"
    }},
    ...
}}

Extract all fields now:
"""
        return prompt

    def get_usage_stats(self) -> Dict[str, Any]:
        """Get usage statistics for the extraction client."""
        return {
            "total_extractions": self.extraction_count,
            "total_tokens_used": self.total_tokens_used,
            "model": self.model,
            "timestamp": datetime.now().isoformat()
        }

    async def close(self):
        """Close the client connection."""
        await self.client.close()
        logger.info(f"OpenAI client closed. Total extractions: {self.extraction_count}, Total tokens: {self.total_tokens_used}")
