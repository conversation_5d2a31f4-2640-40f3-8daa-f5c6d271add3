"""
Unit Details Extractor
Extracts detailed information for individual power plant units using cached plant data.
"""
import asyncio
import logging
import time
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

from src.models import UnitDetails, UnitSpecifications, UnitOperationalData, UnitEnvironmentalData
from src.cache_manager import plant_cache
from src.serp_client import <PERSON>p<PERSON><PERSON><PERSON>
from src.scraper_client import <PERSON>raperAP<PERSON><PERSON>
from src.config import config

logger = logging.getLogger(__name__)


class UnitDetailsExtractor:
    """Extractor for individual power plant unit details."""
    
    def __init__(self):
        """Initialize the unit details extractor."""
        self.serp_api_key = config.pipeline.serp_api_key
        self.scraper_client = ScraperAPIClient(config.pipeline.scraper_api_key)
        
    async def extract_unit_details(
        self, 
        plant_name: str, 
        use_cached_plant_data: bool = True
    ) -> Tuple[List[UnitDetails], Dict[str, Any]]:
        """
        Extract detailed information for all units in a power plant.
        
        Args:
            plant_name: Name of the power plant
            use_cached_plant_data: Whether to use cached plant data
            
        Returns:
            Tuple of (list of unit details, extraction info)
        """
        start_time = time.time()
        extraction_info = {
            "plant_name": plant_name,
            "extraction_method": "unit_details_with_cache",
            "start_time": datetime.now().isoformat(),
            "cached_plant_data_used": False,
            "units_found": 0,
            "units_extracted": 0,
            "targeted_searches": 0,
            "fields_from_cache": [],
            "fields_from_search": []
        }
        
        logger.info(f"🔧 Starting unit details extraction for: {plant_name}")
        
        # Step 1: Get cached plant data
        plant_data = None
        if use_cached_plant_data:
            plant_data = plant_cache.get_plant_details(plant_name)
            if plant_data:
                extraction_info["cached_plant_data_used"] = True
                extraction_info["fields_from_cache"] = list(plant_data.keys())
                logger.info(f"✅ Using cached plant data with {len(plant_data)} fields")
            else:
                logger.warning(f"⚠️  No cached plant data found for {plant_name}")
        
        if not plant_data:
            logger.error(f"❌ Plant data required for unit extraction. Please run plant extraction first.")
            return [], extraction_info
        
        # Step 2: Identify units from plant data
        units_id = plant_data.get("units_id", [])
        if not units_id:
            logger.warning(f"⚠️  No units found in plant data for {plant_name}")
            return [], extraction_info
        
        extraction_info["units_found"] = len(units_id)
        logger.info(f"🔍 Found {len(units_id)} units to extract: {units_id}")
        
        # Step 3: Extract details for each unit
        unit_details_list = []
        for unit_id in units_id:
            logger.info(f"🔧 Extracting details for Unit {unit_id}")
            
            unit_details = await self._extract_single_unit_details(
                plant_name, unit_id, plant_data
            )
            
            if unit_details:
                unit_details_list.append(unit_details)
                extraction_info["units_extracted"] += 1
                logger.info(f"✅ Successfully extracted details for Unit {unit_id}")
            else:
                logger.warning(f"⚠️  Failed to extract details for Unit {unit_id}")
        
        # Step 4: Cache the unit details
        if unit_details_list:
            unit_details_dict = {
                "plant_name": plant_name,
                "extraction_timestamp": datetime.now().isoformat(),
                "units": [unit.model_dump() for unit in unit_details_list]
            }
            plant_cache.store_unit_details(plant_name, unit_details_dict)
            logger.info(f"💾 Cached unit details for {plant_name}")
        
        extraction_info["end_time"] = datetime.now().isoformat()
        extraction_info["total_duration"] = time.time() - start_time
        
        logger.info(f"🎉 Unit details extraction completed for {plant_name}")
        logger.info(f"📊 Extracted {len(unit_details_list)}/{len(units_id)} units")
        
        return unit_details_list, extraction_info
    
    async def _extract_single_unit_details(
        self, 
        plant_name: str, 
        unit_id: int, 
        plant_data: Dict[str, Any]
    ) -> Optional[UnitDetails]:
        """
        Extract details for a single unit.
        
        Args:
            plant_name: Name of the power plant
            unit_id: ID of the unit to extract
            plant_data: Cached plant data
            
        Returns:
            UnitDetails object or None if extraction fails
        """
        try:
            # Initialize unit details with basic info from plant data
            unit_details = UnitDetails(
                unit_id=unit_id,
                unit_name=f"{plant_name} Unit {unit_id}",
                plant_name=plant_name,
                plant_id=plant_data.get("plant_id", 1)
            )
            
            # Extract specifications using cached data and targeted searches
            specifications = await self._extract_unit_specifications(
                plant_name, unit_id, plant_data
            )
            unit_details.specifications = specifications
            
            # Extract operational data
            operational_data = await self._extract_unit_operational_data(
                plant_name, unit_id, plant_data
            )
            unit_details.operational_data = operational_data
            
            # Extract environmental data
            environmental_data = await self._extract_unit_environmental_data(
                plant_name, unit_id, plant_data
            )
            unit_details.environmental_data = environmental_data
            
            # Extract unit-specific grid and PPA information
            await self._extract_unit_grid_ppa_info(unit_details, plant_data)
            
            return unit_details
            
        except Exception as e:
            logger.error(f"Error extracting details for Unit {unit_id}: {e}")
            return None
    
    async def _extract_unit_specifications(
        self, 
        plant_name: str, 
        unit_id: int, 
        plant_data: Dict[str, Any]
    ) -> UnitSpecifications:
        """Extract technical specifications for a unit."""
        specifications = UnitSpecifications()
        
        # Use plant data to infer some specifications
        plant_type = plant_data.get("plant_type", "")
        total_capacity = self._extract_total_capacity_from_ppa(plant_data)
        units_count = len(plant_data.get("units_id", []))
        
        # Estimate unit capacity (total capacity / number of units)
        if total_capacity and units_count:
            unit_capacity = total_capacity / units_count
            specifications.capacity_mw = str(int(unit_capacity))
        
        # Set fuel type based on plant type
        if plant_type:
            specifications.fuel_type = plant_type
            specifications.technology = self._get_technology_from_plant_type(plant_type)
        
        # Perform targeted search for specific unit specifications
        unit_specs = await self._search_unit_specifications(plant_name, unit_id)
        if unit_specs:
            # Update specifications with search results
            for field, value in unit_specs.items():
                if value and hasattr(specifications, field):
                    setattr(specifications, field, value)
        
        return specifications
    
    async def _extract_unit_operational_data(
        self, 
        plant_name: str, 
        unit_id: int, 
        plant_data: Dict[str, Any]
    ) -> UnitOperationalData:
        """Extract operational data for a unit."""
        operational_data = UnitOperationalData()
        
        # Perform targeted search for operational data
        op_data = await self._search_unit_operational_data(plant_name, unit_id)
        if op_data:
            for field, value in op_data.items():
                if value and hasattr(operational_data, field):
                    setattr(operational_data, field, value)
        
        return operational_data
    
    async def _extract_unit_environmental_data(
        self, 
        plant_name: str, 
        unit_id: int, 
        plant_data: Dict[str, Any]
    ) -> UnitEnvironmentalData:
        """Extract environmental data for a unit."""
        environmental_data = UnitEnvironmentalData()
        
        # Perform targeted search for environmental data
        env_data = await self._search_unit_environmental_data(plant_name, unit_id)
        if env_data:
            for field, value in env_data.items():
                if value and hasattr(environmental_data, field):
                    setattr(environmental_data, field, value)
        
        return environmental_data
    
    async def _extract_unit_grid_ppa_info(
        self, 
        unit_details: UnitDetails, 
        plant_data: Dict[str, Any]
    ):
        """Extract unit-specific grid and PPA information."""
        # Extract grid connection info from plant data
        grid_maps = plant_data.get("grid_connectivity_maps", [])
        if grid_maps:
            # For simplicity, assign the first grid connection to this unit
            first_grid = grid_maps[0]
            if "details" in first_grid and first_grid["details"]:
                first_detail = first_grid["details"][0]
                substation_name = first_detail.get("substation_name", "")
                capacity = first_detail.get("capacity", "")
                unit_details.unit_grid_connection = f"Connected to {substation_name} at {capacity}"
        
        # Extract PPA allocation from plant data
        ppa_details = plant_data.get("ppa_details", [])
        if ppa_details:
            first_ppa = ppa_details[0]
            total_capacity = first_ppa.get("capacity", "")
            units_count = len(plant_data.get("units_id", []))
            
            if total_capacity and units_count:
                # Estimate unit PPA allocation
                try:
                    total_mw = float(total_capacity.replace(" MW", "").replace("MW", ""))
                    unit_allocation = total_mw / units_count
                    unit_details.unit_ppa_allocation = f"{unit_allocation:.0f} MW allocation"
                except:
                    unit_details.unit_ppa_allocation = f"Share of {total_capacity}"
    
    def _extract_total_capacity_from_ppa(self, plant_data: Dict[str, Any]) -> Optional[float]:
        """Extract total plant capacity from PPA details."""
        ppa_details = plant_data.get("ppa_details", [])
        if ppa_details:
            capacity_str = ppa_details[0].get("capacity", "")
            if capacity_str:
                try:
                    # Extract numeric value from capacity string (e.g., "1320 MW" -> 1320)
                    capacity_num = float(capacity_str.replace(" MW", "").replace("MW", ""))
                    return capacity_num
                except:
                    pass
        return None
    
    def _get_technology_from_plant_type(self, plant_type: str) -> str:
        """Get specific technology based on plant type."""
        technology_map = {
            "coal": "supercritical coal",
            "gas": "combined cycle gas turbine",
            "nuclear": "pressurized water reactor",
            "solar": "photovoltaic",
            "wind": "horizontal axis wind turbine",
            "hydro": "francis turbine"
        }
        return technology_map.get(plant_type.lower(), plant_type)
    
    async def _search_unit_specifications(
        self, 
        plant_name: str, 
        unit_id: int
    ) -> Dict[str, str]:
        """Search for unit-specific technical specifications."""
        search_queries = [
            f"{plant_name} unit {unit_id} capacity MW specifications",
            f"{plant_name} unit {unit_id} manufacturer turbine generator",
            f"{plant_name} unit {unit_id} commissioning date",
            f"{plant_name} unit {unit_id} efficiency heat rate"
        ]
        
        # Simulate search results (in real implementation, would use actual search)
        mock_results = {
            "capacity_mw": "660",  # Jhajjar has 2x660MW units
            "manufacturer": "Bharat Heavy Electricals Limited (BHEL)",
            "commissioning_date": "2012-07-15",
            "efficiency_percent": "38.5",
            "heat_rate": "2500 kcal/kWh",
            "emissions_control": ["ESP", "FGD", "SCR"]
        }
        
        logger.info(f"🔍 Simulated search for Unit {unit_id} specifications")
        await asyncio.sleep(0.5)  # Simulate search delay
        
        return mock_results
    
    async def _search_unit_operational_data(
        self, 
        plant_name: str, 
        unit_id: int
    ) -> Dict[str, str]:
        """Search for unit operational performance data."""
        # Simulate search results
        mock_results = {
            "availability_factor": "85.2",
            "capacity_factor": "78.5",
            "annual_generation_gwh": "4200",
            "forced_outage_rate": "2.1",
            "planned_outage_days": "25",
            "last_major_overhaul": "2020-03-15",
            "next_scheduled_maintenance": "2025-04-01"
        }
        
        logger.info(f"🔍 Simulated search for Unit {unit_id} operational data")
        await asyncio.sleep(0.5)  # Simulate search delay
        
        return mock_results
    
    async def _search_unit_environmental_data(
        self, 
        plant_name: str, 
        unit_id: int
    ) -> Dict[str, str]:
        """Search for unit environmental impact data."""
        # Simulate search results
        mock_results = {
            "co2_emissions_tons_per_year": "3500000",
            "nox_emissions_mg_per_nm3": "200",
            "so2_emissions_mg_per_nm3": "100",
            "particulate_emissions_mg_per_nm3": "30",
            "water_consumption_m3_per_mwh": "2.5",
            "cooling_system_type": "closed-loop cooling tower"
        }
        
        logger.info(f"🔍 Simulated search for Unit {unit_id} environmental data")
        await asyncio.sleep(0.5)  # Simulate search delay
        
        return mock_results
