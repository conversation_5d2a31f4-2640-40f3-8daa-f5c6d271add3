#!/usr/bin/env python3
"""
Unit-level extraction script for power plant units.
Usage: python run_unit_extraction.py "Plant Name" [unit_count]
"""

import asyncio
import sys
import os
import json
from datetime import datetime
from unittest.mock import MagicMock

# Mock problematic dependencies before importing
sys.modules['src.openai_client'] = MagicMock()

from src.sequential_web_extractor import SequentialWebExtractor
from src.config import config

async def run_unit_extraction(plant_name: str, unit_count: int = 2):
    """Run unit-level extraction for a specific plant."""
    
    print(f"⚡ UNIT-LEVEL EXTRACTION")
    print("=" * 60)
    print(f"🏭 Plant: {plant_name}")
    print(f"🔧 Units to extract: {unit_count}")
    print(f"📋 Template: unit_details.json structure")
    print(f"🌐 Method: Field-by-field web search")
    print("=" * 60)
    
    try:
        # Initialize the sequential extractor
        extractor = SequentialWebExtractor(
            serp_api_key=config.pipeline.scraper_api_key,
            scraper_api_key=config.pipeline.scraper_api_key,
            groq_api_key=config.pipeline.groq_api_key
        )
        
        # Load unit template
        try:
            with open('unit_details.json', 'r') as f:
                unit_template = json.load(f)
            print(f"📋 Loaded template with {len(unit_template)} fields")
        except FileNotFoundError:
            print("❌ unit_details.json template not found")
            return False
        
        # Get cached organizational and plant data first
        print("\n📊 Checking for cached organizational data...")
        org_details = extractor._get_cached_org_details(plant_name)
        if org_details:
            print("✅ Found cached organizational data")
        else:
            print("⚠️  No cached org data - run full sequential extraction first")
            return False
        
        print("\n🏭 Checking for cached plant data...")
        from src.cache_manager import plant_cache
        plant_details = plant_cache.get_plant_details(plant_name)
        if plant_details:
            print("✅ Found cached plant data")
        else:
            print("⚠️  No cached plant data - run full sequential extraction first")
            return False
        
        # Extract unit details
        print(f"\n⚡ Starting unit extraction for {unit_count} units...")
        start_time = datetime.now()
        
        all_units_data = {
            "plant_name": plant_name,
            "extraction_timestamp": datetime.now().isoformat(),
            "units": []
        }
        
        # Extract each unit
        for unit_id in range(1, unit_count + 1):
            print(f"\n🔧 Extracting Unit {unit_id}...")
            
            unit_data, unit_info = await extractor._extract_single_unit_details(
                plant_name, unit_id, unit_template, org_details, plant_details
            )
            
            all_units_data["units"].append(unit_data)
            
            # Show progress
            fields_extracted = sum(1 for v in unit_data.values() if v not in [None, "", []])
            print(f"   ✅ Unit {unit_id}: {fields_extracted}/{len(unit_template)} fields extracted")
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"unit_extraction_results/{plant_name.lower().replace(' ', '_')}_units_{timestamp}.json"
        
        os.makedirs("unit_extraction_results", exist_ok=True)
        with open(filename, 'w') as f:
            json.dump(all_units_data, f, indent=2)
        
        # Display summary
        print(f"\n✅ Unit extraction completed in {duration:.1f} seconds!")
        print(f"⚡ Units extracted: {len(all_units_data['units'])}")
        
        total_fields = 0
        for unit in all_units_data['units']:
            unit_fields = sum(1 for v in unit.values() if v not in [None, "", []])
            total_fields += unit_fields
            print(f"   🔧 Unit {unit['unit_number']}: {unit_fields}/{len(unit_template)} fields")
        
        print(f"📊 Total fields extracted: {total_fields}")
        print(f"📁 Results saved to: {filename}")
        
        # Show sample data
        if all_units_data['units']:
            sample_unit = all_units_data['units'][0]
            print(f"\n📋 Sample Unit Data (Unit 1):")
            for key, value in list(sample_unit.items())[:10]:  # Show first 10 fields
                if value not in [None, "", []]:
                    print(f"   {key}: {value}")
            if len(sample_unit) > 10:
                print(f"   ... and {len(sample_unit) - 10} more fields")
        
        return True
        
    except Exception as e:
        print(f"❌ Unit extraction failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function."""
    if len(sys.argv) < 2:
        print("Usage: python run_unit_extraction.py \"Plant Name\" [unit_count]")
        print("Example: python run_unit_extraction.py \"Jhajjar Power Plant\"")
        print("Example: python run_unit_extraction.py \"Jhajjar Power Plant\" 4")
        print("\nNote: Requires cached org and plant data. Run sequential extraction first if needed.")
        sys.exit(1)
    
    plant_name = sys.argv[1]
    unit_count = int(sys.argv[2]) if len(sys.argv) > 2 else 2
    
    # Check API keys
    if not config.pipeline.scraper_api_key:
        print("❌ SCRAPER_API_KEY not found in environment variables")
        sys.exit(1)
    
    if not config.pipeline.groq_api_key:
        print("❌ GROQ_API_KEY not found in environment variables")
        sys.exit(1)
    
    # Run extraction
    success = asyncio.run(run_unit_extraction(plant_name, unit_count))
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
