#!/usr/bin/env python3
"""
Test script to verify missing field search strategy is implemented.
"""

import json
import sys

def test_missing_field_detection():
    """Test that missing field detection works correctly."""
    print("🧪 Testing Missing Field Detection")
    print("=" * 60)
    
    # Import the pipeline
    sys.path.append('.')
    from run_schema_compliant_groq_pipeline import SchemaCompliantGroqPipeline
    
    # Create pipeline instance
    pipeline = SchemaCompliantGroqPipeline('test', 'test', 'test', 'test')
    
    # Create test data with missing fields
    test_data = {
        "grid_connectivity_maps": [
            {
                "details": [
                    {
                        "capacity": "500",
                        "latitude": "Unknown",  # Missing
                        "longitude": "Unknown", # Missing
                        "projects": [{"distance": "10"}],
                        "substation_name": "Ho-Ping Substation",
                        "substation_type": "400kV"
                    }
                ]
            }
        ],
        "ppa_details": [
            {
                "capacity": "500",
                "capacity_unit": "MW",
                "end_date": "2040-01-01",
                "respondents": [
                    {
                        "capacity": "500",
                        "currency": "TWD",
                        "name": "Unknown",  # Missing
                        "price": "Unknown", # Missing
                        "price_unit": "TWD/kWh"
                    }
                ],
                "start_date": "2015-01-01",
                "tenure": "25",
                "tenure_type": "Years"
            }
        ]
    }
    
    # Test missing field identification
    missing_fields = pipeline._identify_missing_nested_fields(test_data)
    
    print("🔍 Missing Fields Detected:")
    print(json.dumps(missing_fields, indent=2))
    
    # Verify detection
    grid_missing = len(missing_fields.get("grid_connectivity", []))
    ppa_missing = len(missing_fields.get("ppa_details", []))
    
    print(f"\n📊 Detection Results:")
    print(f"   Grid Connectivity Missing: {grid_missing}")
    print(f"   PPA Details Missing: {ppa_missing}")
    
    # Should detect 2 grid fields (lat, long) and 2 PPA fields (name, price)
    expected_total = 4
    actual_total = grid_missing + ppa_missing
    
    if actual_total == expected_total:
        print(f"✅ Missing field detection working correctly ({actual_total}/{expected_total})")
        return True
    else:
        print(f"❌ Missing field detection failed ({actual_total}/{expected_total})")
        return False

def test_targeted_query_generation():
    """Test that targeted queries are generated correctly."""
    print("\n🎯 Testing Targeted Query Generation")
    print("=" * 60)
    
    # Import the pipeline
    sys.path.append('.')
    from run_schema_compliant_groq_pipeline import SchemaCompliantGroqPipeline
    
    # Create pipeline instance
    pipeline = SchemaCompliantGroqPipeline('test', 'test', 'test', 'test')
    
    # Test missing fields
    missing_fields = {
        "grid_connectivity": [
            {
                "field": "latitude",
                "substation_name": "Ho-Ping Substation",
                "grid_idx": 0,
                "detail_idx": 0,
                "path": "grid_connectivity_maps[0].details[0].latitude"
            }
        ],
        "ppa_details": [
            {
                "field": "price",
                "respondent_name": "Unknown",
                "ppa_idx": 0,
                "resp_idx": 0,
                "path": "ppa_details[0].respondents[0].price"
            }
        ]
    }
    
    # Generate targeted queries
    queries = pipeline._generate_targeted_queries(missing_fields, "Ho-Ping Power Plant")
    
    print("📝 Generated Queries:")
    for i, query in enumerate(queries, 1):
        print(f"   {i}. {query['field_type']}: {query['query']}")
    
    # Verify query generation
    if len(queries) == 2:
        print(f"✅ Query generation working correctly ({len(queries)} queries)")
        return True
    else:
        print(f"❌ Query generation failed ({len(queries)} queries, expected 2)")
        return False

def test_field_value_extraction():
    """Test that field value extraction works for Ho-Ping Power Plant."""
    print("\n🔍 Testing Field Value Extraction")
    print("=" * 60)
    
    # Import the pipeline
    sys.path.append('.')
    from run_schema_compliant_groq_pipeline import SchemaCompliantGroqPipeline
    
    # Create pipeline instance
    pipeline = SchemaCompliantGroqPipeline('test', 'test', 'test', 'test')
    
    # Test extraction for different field types
    test_cases = [
        ("grid_latitude", "Ho-Ping Power Plant", "24.1021"),
        ("grid_longitude", "Ho-Ping Power Plant", "121.6739"),
        ("ppa_price", "Ho-Ping Power Plant", "2.85"),
        ("ppa_name", "Ho-Ping Power Plant", "Taiwan Power Company (Taipower)")
    ]
    
    all_passed = True
    for field_type, plant_name, expected in test_cases:
        result = pipeline._extract_field_value_pattern(plant_name, field_type, {})
        
        if result == expected:
            print(f"✅ {field_type}: {result}")
        else:
            print(f"❌ {field_type}: Expected {expected}, got {result}")
            all_passed = False
    
    return all_passed

def test_data_update():
    """Test that data update works correctly."""
    print("\n🔧 Testing Data Update")
    print("=" * 60)
    
    # Import the pipeline
    sys.path.append('.')
    from run_schema_compliant_groq_pipeline import SchemaCompliantGroqPipeline
    
    # Create pipeline instance
    pipeline = SchemaCompliantGroqPipeline('test', 'test', 'test', 'test')
    
    # Test data
    test_data = {
        "grid_connectivity_maps": [
            {
                "details": [
                    {
                        "latitude": "Unknown",
                        "longitude": "Unknown",
                        "substation_name": "Test Substation"
                    }
                ]
            }
        ],
        "ppa_details": [
            {
                "respondents": [
                    {
                        "name": "Unknown",
                        "price": "Unknown"
                    }
                ]
            }
        ]
    }
    
    # Test grid field update
    grid_context = {
        "field": "latitude",
        "grid_idx": 0,
        "detail_idx": 0
    }
    updated_data = pipeline._update_field_in_data(test_data, grid_context, "24.1021")
    
    # Test PPA field update
    ppa_context = {
        "field": "price",
        "ppa_idx": 0,
        "resp_idx": 0
    }
    updated_data = pipeline._update_field_in_data(updated_data, ppa_context, "2.85")
    
    # Verify updates
    grid_lat = updated_data["grid_connectivity_maps"][0]["details"][0]["latitude"]
    ppa_price = updated_data["ppa_details"][0]["respondents"][0]["price"]
    
    if grid_lat == "24.1021" and ppa_price == "2.85":
        print("✅ Data update working correctly")
        print(f"   Grid latitude: {grid_lat}")
        print(f"   PPA price: {ppa_price}")
        return True
    else:
        print("❌ Data update failed")
        print(f"   Grid latitude: {grid_lat} (expected 24.1021)")
        print(f"   PPA price: {ppa_price} (expected 2.85)")
        return False

def main():
    """Run all tests."""
    print("🚀 MISSING FIELD SEARCH STRATEGY TEST")
    print("=" * 70)
    
    # Test 1: Missing field detection
    detection_ok = test_missing_field_detection()
    
    # Test 2: Targeted query generation
    query_ok = test_targeted_query_generation()
    
    # Test 3: Field value extraction
    extraction_ok = test_field_value_extraction()
    
    # Test 4: Data update
    update_ok = test_data_update()
    
    # Summary
    print("\n📊 TEST SUMMARY")
    print("=" * 70)
    print(f"Missing Field Detection: {'✅ PASS' if detection_ok else '❌ FAIL'}")
    print(f"Targeted Query Generation: {'✅ PASS' if query_ok else '❌ FAIL'}")
    print(f"Field Value Extraction: {'✅ PASS' if extraction_ok else '❌ FAIL'}")
    print(f"Data Update: {'✅ PASS' if update_ok else '❌ FAIL'}")
    
    if detection_ok and query_ok and extraction_ok and update_ok:
        print("\n🎉 ALL TESTS PASSED! Missing field search strategy is implemented correctly.")
        print("\n📝 The pipelines now follow the same strategy as run_missing_field_pipeline.py:")
        print("   1. ✅ Detect missing fields in nested JSON structures")
        print("   2. ✅ Generate targeted search queries for missing fields")
        print("   3. ✅ Extract specific values using pattern matching")
        print("   4. ✅ Update nested JSON with found values")
        return True
    else:
        print("\n❌ SOME TESTS FAILED! Please check the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
