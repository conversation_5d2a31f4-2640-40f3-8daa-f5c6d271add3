#!/usr/bin/env python3
"""
Test script for Ho-Ping Power Plant with OpenAI pipeline.
"""

import os
import sys
import asyncio

def test_country_detection():
    """Test country detection for Ho-Ping Power Plant."""
    print("🧪 Testing Country Detection for Ho-Ping Power Plant")
    print("=" * 60)
    
    # Import the pipeline
    sys.path.append('.')
    from run_vision_enhanced_openai_pipeline import VisionEnhancedPipeline
    
    # Create pipeline instance
    pipeline = VisionEnhancedPipeline('test', 'test', 'test', 'test')
    
    # Test Ho-Ping Power Plant
    plant_name = "Ho-Ping Power Plant"
    defaults = pipeline._get_country_defaults(plant_name)
    
    print(f"🏭 Plant Name: {plant_name}")
    print(f"🌍 Country: {defaults['country']}")
    print(f"💰 Currency: {defaults['currency']}")
    print(f"📅 Fiscal Year: {defaults['financial_year']}")
    print(f"🏢 Organization: {defaults['organization']}")
    print(f"📍 Province: {defaults['province']}")
    
    # Verify Taiwan detection
    if defaults['country'] == 'Taiwan' and defaults['currency'] == 'TWD':
        print("✅ Taiwan detection working correctly!")
        return True
    else:
        print("❌ Taiwan detection failed!")
        return False

def test_environment_setup():
    """Test that environment is set up correctly."""
    print("\n🔧 Testing Environment Setup")
    print("=" * 60)
    
    # Check PLANT_NAME
    plant_name = os.getenv('PLANT_NAME')
    if plant_name:
        print(f"✅ PLANT_NAME: {plant_name}")
    else:
        print("❌ PLANT_NAME not set")
        return False
    
    # Check API keys
    api_keys = ['SERP_API_KEY', 'SCRAPER_API_KEY', 'OPENAI_API_KEY']
    for key in api_keys:
        value = os.getenv(key)
        if value:
            print(f"✅ {key}: {value[:10]}...")
        else:
            print(f"❌ {key}: Not set")
            return False
    
    return True

async def test_minimal_extraction():
    """Test minimal extraction without full pipeline."""
    print("\n🚀 Testing Minimal Extraction")
    print("=" * 60)
    
    try:
        # Import required modules
        sys.path.append('.')
        from src.vision_enhanced_openai_client import VisionEnhancedOpenAIClient
        
        # Get API key
        openai_api_key = os.getenv('OPENAI_API_KEY')
        if not openai_api_key:
            print("❌ OpenAI API key not found")
            return False
        
        # Create client
        client = VisionEnhancedOpenAIClient(openai_api_key, "gpt-4o-mini")
        print("✅ OpenAI client created successfully")
        
        # Test simple field extraction
        test_content = "Ho-Ping Power Plant is a coal-fired power plant located in Taiwan. It is operated by Taiwan Power Company (Taipower)."
        
        result = await client.extract_field_text(
            field_name="country_name",
            content=test_content,
            context="Testing Ho-Ping Power Plant"
        )
        
        if result:
            print(f"✅ Field extraction successful: {result.extracted_value}")
            print(f"📊 Confidence: {result.confidence_score}")
            return True
        else:
            print("❌ Field extraction failed")
            return False
            
    except Exception as e:
        print(f"❌ Error during minimal extraction: {e}")
        return False

async def main():
    """Run all tests."""
    print("🧪 HO-PING POWER PLANT TEST SUITE")
    print("=" * 70)
    
    # Test 1: Country detection
    country_ok = test_country_detection()
    
    # Test 2: Environment setup
    env_ok = test_environment_setup()
    
    # Test 3: Minimal extraction
    if env_ok:
        extract_ok = await test_minimal_extraction()
    else:
        extract_ok = False
        print("⏭️  Skipping extraction test due to environment issues")
    
    # Summary
    print("\n📊 TEST SUMMARY")
    print("=" * 70)
    print(f"Country Detection: {'✅ PASS' if country_ok else '❌ FAIL'}")
    print(f"Environment Setup: {'✅ PASS' if env_ok else '❌ FAIL'}")
    print(f"Minimal Extraction: {'✅ PASS' if extract_ok else '❌ FAIL'}")
    
    if country_ok and env_ok and extract_ok:
        print("\n🎉 ALL TESTS PASSED! Ready to run full pipeline.")
        print("\n🚀 To run full extraction:")
        print("export PLANT_NAME='Ho-Ping Power Plant'")
        print("python run_vision_enhanced_openai_pipeline.py")
        return True
    else:
        print("\n❌ SOME TESTS FAILED! Please check the issues above.")
        return False

if __name__ == "__main__":
    # Set plant name for testing
    os.environ['PLANT_NAME'] = 'Ho-Ping Power Plant'
    
    # Run tests
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
