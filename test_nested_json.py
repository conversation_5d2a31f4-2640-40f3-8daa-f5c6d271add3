#!/usr/bin/env python3
"""
Test script to verify nested JSON structures for grid_connectivity_maps and ppa_details.
"""

import json
import sys

def test_nested_json_structures():
    """Test that nested JSON structures match the schema exactly."""
    print("🧪 Testing Nested JSON Structures")
    print("=" * 60)
    
    # Import the pipeline
    sys.path.append('.')
    from run_schema_compliant_groq_pipeline import SchemaCompliantGroqPipeline
    
    # Create pipeline instance
    pipeline = SchemaCompliantGroqPipeline('test', 'test', 'test', 'test')
    
    # Test plant fallback for Ho-Ping Power Plant (Taiwan)
    plant_name = "Ho-Ping Power Plant"
    
    # Test grid_connectivity_maps
    grid_fallback = pipeline._get_schema_compliant_plant_fallback("grid_connectivity_maps", plant_name)
    print("🔌 GRID CONNECTIVITY MAPS:")
    print(json.dumps(grid_fallback, indent=2))
    
    # Verify structure
    if isinstance(grid_fallback, list) and len(grid_fallback) > 0:
        grid_item = grid_fallback[0]
        if "details" in grid_item and isinstance(grid_item["details"], list):
            detail = grid_item["details"][0]
            required_fields = ["capacity", "latitude", "longitude", "projects", "substation_name", "substation_type"]
            if all(field in detail for field in required_fields):
                print("✅ Grid connectivity structure is correct")
                grid_ok = True
            else:
                print("❌ Grid connectivity missing required fields")
                grid_ok = False
        else:
            print("❌ Grid connectivity structure is invalid")
            grid_ok = False
    else:
        print("❌ Grid connectivity is not a valid array")
        grid_ok = False
    
    print("\n" + "="*60)
    
    # Test ppa_details
    ppa_fallback = pipeline._get_schema_compliant_plant_fallback("ppa_details", plant_name)
    print("📄 PPA DETAILS:")
    print(json.dumps(ppa_fallback, indent=2))
    
    # Verify structure
    if isinstance(ppa_fallback, list) and len(ppa_fallback) > 0:
        ppa_item = ppa_fallback[0]
        required_fields = ["capacity", "capacity_unit", "end_date", "respondents", "start_date", "tenure", "tenure_type"]
        if all(field in ppa_item for field in required_fields):
            # Check respondents structure
            if isinstance(ppa_item["respondents"], list) and len(ppa_item["respondents"]) > 0:
                respondent = ppa_item["respondents"][0]
                respondent_fields = ["capacity", "currency", "name", "price", "price_unit"]
                if all(field in respondent for field in respondent_fields):
                    print("✅ PPA details structure is correct")
                    ppa_ok = True
                else:
                    print("❌ PPA respondent missing required fields")
                    ppa_ok = False
            else:
                print("❌ PPA respondents structure is invalid")
                ppa_ok = False
        else:
            print("❌ PPA details missing required fields")
            ppa_ok = False
    else:
        print("❌ PPA details is not a valid array")
        ppa_ok = False
    
    return grid_ok and ppa_ok

def test_schema_compliance():
    """Test that the structures match the actual schema."""
    print("\n🔍 Testing Schema Compliance")
    print("=" * 60)
    
    try:
        # Load the actual plant schema
        with open('Final Pipeline/plant_level.json', 'r') as f:
            schema = json.load(f)
        
        # Check grid_connectivity_maps schema
        grid_schema = schema.get("grid_connectivity_maps", {})
        print("📋 Schema grid_connectivity_maps structure:")
        print(json.dumps(grid_schema, indent=2))
        
        print("\n" + "="*40)
        
        # Check ppa_details schema
        ppa_schema = schema.get("ppa_details", {})
        print("📋 Schema ppa_details structure:")
        print(json.dumps(ppa_schema, indent=2))
        
        print("✅ Schema loaded successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error loading schema: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 NESTED JSON STRUCTURE TEST")
    print("=" * 70)
    
    # Test 1: Nested JSON structures
    nested_ok = test_nested_json_structures()
    
    # Test 2: Schema compliance
    schema_ok = test_schema_compliance()
    
    # Summary
    print("\n📊 TEST SUMMARY")
    print("=" * 70)
    print(f"Nested JSON Structures: {'✅ PASS' if nested_ok else '❌ FAIL'}")
    print(f"Schema Compliance:      {'✅ PASS' if schema_ok else '❌ FAIL'}")
    
    if nested_ok and schema_ok:
        print("\n🎉 ALL TESTS PASSED! Nested JSON structures are correctly implemented.")
        print("\n📝 The pipelines will now output:")
        print("   • grid_connectivity_maps: Properly nested JSON with details, projects, substation info")
        print("   • ppa_details: Properly nested JSON with capacity, dates, respondents, tenure")
        return True
    else:
        print("\n❌ SOME TESTS FAILED! Please check the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
