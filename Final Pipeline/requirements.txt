# Universal Hierarchical Power Plant Extraction Pipeline
# Core dependencies for the complete pipeline

# OpenAI API for LLM and vision processing
openai>=1.0.0

# HTTP client for async requests
aiohttp>=3.8.0

# PDF processing
PyPDF2>=3.0.0
pdf2image>=1.16.0

# Image processing for vision capabilities
Pillow>=9.0.0

# Data validation and parsing
pydantic>=2.0.0

# Environment variable management
python-dotenv>=1.0.0

# Retry mechanisms
tenacity>=8.0.0

# HTML parsing (optional, for better content extraction)
beautifulsoup4>=4.11.0
lxml>=4.9.0

# Progress bars and CLI utilities
tqdm>=4.64.0
click>=8.0.0

# Logging and monitoring
structlog>=22.0.0

# Data serialization
orjson>=3.8.0

# Date/time utilities
python-dateutil>=2.8.0

# Async utilities
asyncio-throttle>=1.0.0

# Development and testing (optional)
pytest>=7.0.0
pytest-asyncio>=0.21.0
black>=22.0.0
flake8>=5.0.0
mypy>=1.0.0

# Optional: For enhanced PDF processing
# poppler-utils (system dependency)
# tesseract-ocr (system dependency for OCR)
