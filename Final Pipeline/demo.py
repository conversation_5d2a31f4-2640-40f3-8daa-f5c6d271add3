#!/usr/bin/env python3
"""
Demo script showing the Universal Hierarchical Power Plant Extraction Pipeline.
This script demonstrates the pipeline structure and capabilities.
"""

import json
from pathlib import Path

def show_pipeline_overview():
    """Show overview of the pipeline."""
    print("🚀 Universal Hierarchical Power Plant Extraction Pipeline")
    print("=" * 70)
    print()
    print("📋 PIPELINE OVERVIEW:")
    print("   ✅ Complete hierarchical extraction (Org → Plant → Unit)")
    print("   ✅ AI-powered with OpenAI GPT-4o-mini + vision capabilities")
    print("   ✅ Schema-driven extraction (no hardcoding)")
    print("   ✅ Universal compatibility (any power plant globally)")
    print("   ✅ Complete source attribution and tracking")
    print("   ✅ Smart caching and hierarchical data flow")
    print("   ✅ Vision processing for scanned PDFs")
    print()

def show_schema_analysis():
    """Show analysis of the schemas."""
    print("📊 SCHEMA ANALYSIS:")
    print("-" * 40)
    
    schemas = {
        "Organizational": "org_level.json",
        "Plant": "plant_level.json", 
        "Unit": "unit_level.json"
    }
    
    total_fields = 0
    
    for level_name, schema_file in schemas.items():
        try:
            with open(schema_file, 'r') as f:
                schema_data = json.load(f)
            
            field_count = len(schema_data)
            total_fields += field_count
            
            print(f"   {level_name:12}: {field_count:2d} fields")
            
            # Show sample fields
            sample_fields = list(schema_data.keys())[:3]
            print(f"   {'':12}   Sample: {', '.join(sample_fields)}")
            
        except Exception as e:
            print(f"   {level_name:12}: Error loading schema")
    
    print(f"   {'TOTAL':12}: {total_fields:2d} fields across all levels")
    print()

def show_architecture():
    """Show the pipeline architecture."""
    print("🏗️  PIPELINE ARCHITECTURE:")
    print("-" * 40)
    
    components = {
        "core/": "Configuration, models, and exception handling",
        "clients/": "API clients (Scraper API, OpenAI, Search)",
        "processors/": "Schema processing, validation, vision enhancement",
        "extractors/": "Level-specific extractors (Org, Plant, Unit)",
        "orchestrators/": "Pipeline coordination and management",
        "utils/": "Caching, source tracking, field analysis"
    }
    
    for component, description in components.items():
        print(f"   {component:15} {description}")
    
    print()

def show_key_features():
    """Show key features and capabilities."""
    print("🎯 KEY FEATURES:")
    print("-" * 40)
    
    features = [
        "Schema-Driven Extraction: Uses field descriptions for intelligent queries",
        "Vision Processing: Handles scanned PDFs with GPT-4o-mini vision",
        "Hierarchical Data Flow: Org context enhances plant, plant enhances unit",
        "Source Attribution: Complete tracking of all data sources",
        "Smart Caching: Reduces redundant API calls between levels",
        "Global Compatibility: Works for any power plant worldwide",
        "Error Recovery: Robust error handling and retry mechanisms",
        "Performance Optimized: Async processing and rate limiting"
    ]
    
    for i, feature in enumerate(features, 1):
        print(f"   {i}. {feature}")
    
    print()

def show_usage_example():
    """Show usage example."""
    print("💻 USAGE EXAMPLE:")
    print("-" * 40)
    
    example_code = '''
# Command Line Usage
python main.py "Jhajjar Power Plant"

# Python API Usage
import asyncio
from main import UniversalPowerPlantPipeline

async def extract_plant():
    pipeline = UniversalPowerPlantPipeline()
    result = await pipeline.extract_plant_data("Jhajjar Power Plant")
    
    if result.success:
        print(f"✅ Extracted {result.total_fields_extracted} fields")
        print(f"📊 Used {result.total_sources_used} sources")
        print(f"⏱️  Completed in {result.execution_time:.1f} seconds")
    else:
        print(f"❌ Extraction failed: {result.error_message}")

asyncio.run(extract_plant())
'''
    
    print(example_code)

def show_output_format():
    """Show output format."""
    print("📄 OUTPUT FORMAT:")
    print("-" * 40)
    
    output_example = {
        "plant_name": "Jhajjar Power Plant",
        "session_id": "jhajjar_power_plant_20240101_120000",
        "success": True,
        "execution_time": 45.2,
        "total_fields_extracted": 87,
        "total_sources_used": 15,
        "organizational_data": {
            "organization_name": "Ayana Renewable Power",
            "country_name": "India",
            "cfpp_type": "Private",
            "plant_types": ["Coal"],
            "currency_in": "INR"
        },
        "plant_data": {
            "name": "Jhajjar Power Plant",
            "lat": 28.6139,
            "long": 76.7794,
            "plant_type": "Coal",
            "capacity": 1320,
            "units_id": ["1", "2"]
        },
        "unit_data": [
            {
                "unit_number": "1",
                "capacity": 660,
                "technology": "Supercritical",
                "fuel_type": [{"fuel": "Coal", "type": "Bituminous"}]
            }
        ]
    }
    
    print("   Sample Output Structure:")
    print(json.dumps(output_example, indent=4)[:500] + "...")
    print()

def show_installation():
    """Show installation instructions."""
    print("🛠️  INSTALLATION & SETUP:")
    print("-" * 40)
    
    steps = [
        "1. Install dependencies: pip install -r requirements.txt",
        "2. Set up environment variables in .env file:",
        "   SCRAPER_API_KEY=your_scraper_api_key",
        "   OPENAI_API_KEY=your_openai_api_key",
        "   OPENAI_MODEL=gpt-4o-mini",
        "3. Run extraction: python main.py 'Plant Name'"
    ]
    
    for step in steps:
        print(f"   {step}")
    
    print()

def show_file_structure():
    """Show the complete file structure."""
    print("📁 COMPLETE FILE STRUCTURE:")
    print("-" * 40)
    
    # Get actual file structure
    def get_structure(path, prefix="", max_depth=3, current_depth=0):
        if current_depth >= max_depth:
            return []
        
        items = []
        try:
            path_obj = Path(path)
            if path_obj.is_dir():
                for item in sorted(path_obj.iterdir()):
                    if item.name.startswith('.'):
                        continue
                    
                    if item.is_dir():
                        items.append(f"{prefix}├── {item.name}/")
                        sub_items = get_structure(item, prefix + "│   ", max_depth, current_depth + 1)
                        items.extend(sub_items)
                    else:
                        items.append(f"{prefix}├── {item.name}")
        except:
            pass
        
        return items
    
    structure = get_structure(".", max_depth=2)
    for item in structure[:20]:  # Show first 20 items
        print(f"   {item}")
    
    if len(structure) > 20:
        print(f"   ... and {len(structure) - 20} more files")
    
    print()

def main():
    """Main demo function."""
    show_pipeline_overview()
    show_schema_analysis()
    show_architecture()
    show_key_features()
    show_usage_example()
    show_output_format()
    show_installation()
    show_file_structure()
    
    print("🎉 PIPELINE COMPLETE!")
    print("=" * 70)
    print("The Universal Hierarchical Power Plant Extraction Pipeline is")
    print("ready for use. All components have been implemented and tested.")
    print()
    print("Next steps:")
    print("1. Install dependencies: pip install -r requirements.txt")
    print("2. Configure API keys in .env file")
    print("3. Run: python main.py 'Your Power Plant Name'")
    print()
    print("The pipeline will extract complete hierarchical data for any")
    print("power plant globally using AI and web scraping capabilities.")

if __name__ == "__main__":
    main()
