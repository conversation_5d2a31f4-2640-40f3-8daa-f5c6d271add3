"""
Field analyzer for analyzing extraction completeness and identifying missing data.
Provides insights into extraction quality and suggests improvements.
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime

from ..core import ExtractionLevel
from ..processors import SchemaProcessor

logger = logging.getLogger(__name__)


class FieldAnalyzer:
    """Analyzes field extraction completeness and quality."""
    
    def __init__(self, schema_processor: SchemaProcessor):
        """Initialize the field analyzer."""
        self.schema_processor = schema_processor
    
    def analyze_extraction_completeness(
        self,
        level: ExtractionLevel,
        extracted_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Analyze completeness of extracted data for a level.
        
        Args:
            level: Extraction level
            extracted_data: Extracted data to analyze
            
        Returns:
            Analysis results with completeness metrics
        """
        try:
            schema = self.schema_processor.get_schema(level)
            
            analysis = {
                "level": level.value,
                "total_fields": len(schema.fields),
                "extracted_fields": 0,
                "missing_fields": [],
                "empty_fields": [],
                "nested_field_analysis": {},
                "completeness_percentage": 0.0,
                "quality_score": 0.0,
                "field_details": []
            }
            
            for field in schema.fields:
                field_name = field.name
                field_value = extracted_data.get(field_name)
                
                field_detail = {
                    "name": field_name,
                    "description": field.description,
                    "is_nested": field.is_nested,
                    "data_type": field.data_type,
                    "has_value": False,
                    "is_empty": False,
                    "value_preview": None
                }
                
                if field_value is not None:
                    if self._is_empty_value(field_value):
                        analysis["empty_fields"].append(field_name)
                        field_detail["is_empty"] = True
                    else:
                        analysis["extracted_fields"] += 1
                        field_detail["has_value"] = True
                        field_detail["value_preview"] = self._get_value_preview(field_value)
                        
                        # Analyze nested fields
                        if field.is_nested:
                            nested_analysis = self._analyze_nested_field(field, field_value)
                            analysis["nested_field_analysis"][field_name] = nested_analysis
                else:
                    analysis["missing_fields"].append(field_name)
                
                analysis["field_details"].append(field_detail)
            
            # Calculate metrics
            analysis["completeness_percentage"] = (
                analysis["extracted_fields"] / analysis["total_fields"] * 100
                if analysis["total_fields"] > 0 else 0
            )
            
            analysis["quality_score"] = self._calculate_quality_score(analysis, extracted_data)
            
            return analysis
            
        except Exception as e:
            logger.error(f"Failed to analyze extraction completeness: {str(e)}")
            return {"error": str(e)}
    
    def _is_empty_value(self, value: Any) -> bool:
        """Check if a value is considered empty."""
        if value is None:
            return True
        
        if isinstance(value, str):
            return len(value.strip()) == 0
        
        if isinstance(value, (list, dict)):
            return len(value) == 0
        
        return False
    
    def _get_value_preview(self, value: Any, max_length: int = 50) -> str:
        """Get a preview of the value for display."""
        if value is None:
            return "None"
        
        if isinstance(value, str):
            return value[:max_length] + "..." if len(value) > max_length else value
        
        if isinstance(value, (list, dict)):
            return f"{type(value).__name__} with {len(value)} items"
        
        return str(value)[:max_length]
    
    def _analyze_nested_field(self, field_def, field_value: Any) -> Dict[str, Any]:
        """Analyze nested field structure and completeness."""
        analysis = {
            "field_name": field_def.name,
            "expected_structure": field_def.data_type,
            "actual_type": type(field_value).__name__,
            "is_valid_structure": False,
            "nested_completeness": 0.0,
            "issues": []
        }
        
        try:
            if field_def.data_type == "array" and isinstance(field_value, list):
                analysis["is_valid_structure"] = True
                analysis["item_count"] = len(field_value)
                
                if field_def.nested_fields and field_value:
                    # Analyze nested field completeness
                    total_nested_fields = len(field_def.nested_fields)
                    completed_nested_fields = 0
                    
                    for item in field_value:
                        if isinstance(item, dict):
                            for nested_field in field_def.nested_fields:
                                if nested_field.name in item and not self._is_empty_value(item[nested_field.name]):
                                    completed_nested_fields += 1
                    
                    if total_nested_fields > 0:
                        analysis["nested_completeness"] = (
                            completed_nested_fields / (total_nested_fields * len(field_value)) * 100
                        )
                
            elif field_def.data_type == "object" and isinstance(field_value, dict):
                analysis["is_valid_structure"] = True
                
                if field_def.nested_fields:
                    total_nested_fields = len(field_def.nested_fields)
                    completed_nested_fields = 0
                    
                    for nested_field in field_def.nested_fields:
                        if nested_field.name in field_value and not self._is_empty_value(field_value[nested_field.name]):
                            completed_nested_fields += 1
                    
                    analysis["nested_completeness"] = (
                        completed_nested_fields / total_nested_fields * 100
                        if total_nested_fields > 0 else 0
                    )
            else:
                analysis["issues"].append(f"Expected {field_def.data_type}, got {type(field_value).__name__}")
            
        except Exception as e:
            analysis["issues"].append(f"Analysis error: {str(e)}")
        
        return analysis
    
    def _calculate_quality_score(self, analysis: Dict[str, Any], extracted_data: Dict[str, Any]) -> float:
        """Calculate overall quality score for the extraction."""
        try:
            # Base score from completeness
            completeness_score = analysis["completeness_percentage"]
            
            # Penalty for empty fields
            empty_penalty = len(analysis["empty_fields"]) * 5  # 5% penalty per empty field
            
            # Bonus for nested field completeness
            nested_bonus = 0
            nested_analyses = analysis.get("nested_field_analysis", {})
            if nested_analyses:
                avg_nested_completeness = sum(
                    na.get("nested_completeness", 0) for na in nested_analyses.values()
                ) / len(nested_analyses)
                nested_bonus = avg_nested_completeness * 0.2  # 20% weight for nested completeness
            
            # Check for source attribution (if available)
            source_bonus = 0
            field_sources = extracted_data.get("_field_sources", {})
            if field_sources:
                fields_with_sources = sum(
                    1 for source_info in field_sources.values()
                    if source_info.get("confidence", 0) > 0.5
                )
                total_fields = len(field_sources)
                if total_fields > 0:
                    source_bonus = (fields_with_sources / total_fields) * 10  # Up to 10% bonus
            
            # Calculate final score
            quality_score = completeness_score - empty_penalty + nested_bonus + source_bonus
            
            # Ensure score is between 0 and 100
            return max(0, min(100, quality_score))
            
        except Exception as e:
            logger.error(f"Failed to calculate quality score: {str(e)}")
            return 0.0
    
    def identify_missing_critical_fields(
        self,
        level: ExtractionLevel,
        extracted_data: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Identify missing fields that are critical for the extraction level."""
        critical_fields = {
            ExtractionLevel.ORGANIZATIONAL: [
                "organization_name", "country_name", "cfpp_type"
            ],
            ExtractionLevel.PLANT: [
                "name", "lat", "long", "plant_type", "units_id"
            ],
            ExtractionLevel.UNIT: [
                "capacity", "unit_number", "technology", "fuel_type"
            ]
        }
        
        missing_critical = []
        level_critical_fields = critical_fields.get(level, [])
        
        for field_name in level_critical_fields:
            field_value = extracted_data.get(field_name)
            if self._is_empty_value(field_value):
                field_def = self.schema_processor.get_field_definition(level, field_name)
                missing_critical.append({
                    "field_name": field_name,
                    "description": field_def.description if field_def else "No description available",
                    "criticality": "high",
                    "suggested_search_terms": self._generate_search_suggestions(field_name, level)
                })
        
        return missing_critical
    
    def _generate_search_suggestions(self, field_name: str, level: ExtractionLevel) -> List[str]:
        """Generate search term suggestions for missing fields."""
        suggestions = {
            "organization_name": ["company owner", "operator", "parent company"],
            "country_name": ["location", "country", "nation"],
            "cfpp_type": ["ownership", "private", "public", "government"],
            "lat": ["latitude", "coordinates", "location"],
            "long": ["longitude", "coordinates", "location"],
            "plant_type": ["technology", "fuel type", "coal", "gas", "nuclear"],
            "capacity": ["MW", "megawatt", "installed capacity", "generation capacity"],
            "technology": ["supercritical", "subcritical", "combined cycle", "open cycle"],
            "fuel_type": ["coal", "natural gas", "biomass", "fuel source"]
        }
        
        return suggestions.get(field_name, [field_name.replace("_", " ")])
    
    def generate_improvement_recommendations(
        self,
        level: ExtractionLevel,
        analysis: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Generate recommendations for improving extraction quality."""
        recommendations = []
        
        # Completeness recommendations
        if analysis["completeness_percentage"] < 70:
            recommendations.append({
                "type": "completeness",
                "priority": "high",
                "message": f"Only {analysis['completeness_percentage']:.1f}% of fields extracted. Consider additional search queries.",
                "action": "Add more specific search terms for missing fields"
            })
        
        # Missing critical fields
        if analysis.get("missing_fields"):
            critical_missing = len([
                f for f in analysis["missing_fields"]
                if f in ["organization_name", "name", "capacity", "country_name"]
            ])
            
            if critical_missing > 0:
                recommendations.append({
                    "type": "critical_fields",
                    "priority": "high",
                    "message": f"{critical_missing} critical fields are missing",
                    "action": "Focus search efforts on critical field extraction"
                })
        
        # Nested field recommendations
        nested_analyses = analysis.get("nested_field_analysis", {})
        for field_name, nested_analysis in nested_analyses.items():
            if nested_analysis.get("nested_completeness", 0) < 50:
                recommendations.append({
                    "type": "nested_fields",
                    "priority": "medium",
                    "message": f"Nested field '{field_name}' has low completeness",
                    "action": f"Use targeted searches for {field_name} components"
                })
        
        # Quality score recommendations
        if analysis["quality_score"] < 60:
            recommendations.append({
                "type": "quality",
                "priority": "medium",
                "message": f"Overall quality score is {analysis['quality_score']:.1f}%",
                "action": "Review extraction methods and source quality"
            })
        
        return recommendations
    
    def compare_extractions(
        self,
        level: ExtractionLevel,
        extraction1: Dict[str, Any],
        extraction2: Dict[str, Any],
        labels: Tuple[str, str] = ("Extraction 1", "Extraction 2")
    ) -> Dict[str, Any]:
        """Compare two extractions for the same level."""
        comparison = {
            "level": level.value,
            "labels": labels,
            "field_comparisons": [],
            "summary": {
                "total_fields": 0,
                "matching_fields": 0,
                "different_fields": 0,
                "only_in_first": 0,
                "only_in_second": 0
            }
        }
        
        schema = self.schema_processor.get_schema(level)
        
        for field in schema.fields:
            field_name = field.name
            value1 = extraction1.get(field_name)
            value2 = extraction2.get(field_name)
            
            field_comparison = {
                "field_name": field_name,
                "value1": self._get_value_preview(value1),
                "value2": self._get_value_preview(value2),
                "match": self._values_match(value1, value2),
                "in_first": not self._is_empty_value(value1),
                "in_second": not self._is_empty_value(value2)
            }
            
            comparison["field_comparisons"].append(field_comparison)
            comparison["summary"]["total_fields"] += 1
            
            if field_comparison["match"]:
                comparison["summary"]["matching_fields"] += 1
            elif field_comparison["in_first"] and field_comparison["in_second"]:
                comparison["summary"]["different_fields"] += 1
            elif field_comparison["in_first"]:
                comparison["summary"]["only_in_first"] += 1
            elif field_comparison["in_second"]:
                comparison["summary"]["only_in_second"] += 1
        
        return comparison
    
    def _values_match(self, value1: Any, value2: Any) -> bool:
        """Check if two values match (considering empty values)."""
        if self._is_empty_value(value1) and self._is_empty_value(value2):
            return True
        
        if type(value1) != type(value2):
            return False
        
        if isinstance(value1, str):
            return value1.strip().lower() == value2.strip().lower()
        
        return value1 == value2
