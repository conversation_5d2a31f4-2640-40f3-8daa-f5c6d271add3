"""
Source tracker for maintaining attribution of all extracted data.
Tracks sources, extraction methods, and confidence scores for complete data lineage.
"""

import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path

from ..core import ExtractionLevel, ExtractionResult, SourceType

logger = logging.getLogger(__name__)


class SourceTracker:
    """Tracks sources and attribution for all extracted data."""
    
    def __init__(self, output_dir: str = "output"):
        """Initialize the source tracker."""
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Source tracking data
        self.sources_by_level: Dict[ExtractionLevel, List[Dict[str, Any]]] = {
            level: [] for level in ExtractionLevel
        }
        
        self.field_sources: Dict[str, Dict[str, Any]] = {}
        self.extraction_session_id: Optional[str] = None
        
        logger.info(f"Source tracker initialized with output directory: {self.output_dir}")
    
    def start_session(self, plant_name: str) -> str:
        """Start a new extraction session."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.extraction_session_id = f"{plant_name.replace(' ', '_')}_{timestamp}"
        
        logger.info(f"Started source tracking session: {self.extraction_session_id}")
        return self.extraction_session_id
    
    def track_field_extraction(
        self,
        level: ExtractionLevel,
        field_name: str,
        result: ExtractionResult,
        plant_name: str,
        unit_id: Optional[str] = None
    ):
        """Track the source of a field extraction."""
        try:
            field_key = f"{level.value}.{field_name}"
            if unit_id:
                field_key += f".unit_{unit_id}"
            
            source_info = {
                "field_name": field_name,
                "level": level.value,
                "plant_name": plant_name,
                "unit_id": unit_id,
                "extracted_value": result.extracted_value,
                "confidence_score": result.confidence_score,
                "source_url": result.source_url,
                "extraction_method": result.extraction_method,
                "extraction_timestamp": result.extraction_timestamp.isoformat(),
                "raw_content_preview": result.raw_content[:200] if result.raw_content else None
            }
            
            self.field_sources[field_key] = source_info
            
            # Also add to level-specific tracking
            if source_info not in self.sources_by_level[level]:
                self.sources_by_level[level].append(source_info)
            
            logger.debug(f"Tracked source for {field_key}: {result.source_url}")
            
        except Exception as e:
            logger.error(f"Failed to track field extraction: {str(e)}")
    
    def track_content_source(
        self,
        level: ExtractionLevel,
        url: str,
        source_type: SourceType,
        content_length: int,
        document_type: str,
        title: Optional[str] = None
    ):
        """Track a content source used in extraction."""
        try:
            source_info = {
                "url": url,
                "source_type": source_type.value,
                "content_length": content_length,
                "document_type": document_type,
                "title": title,
                "level": level.value,
                "accessed_at": datetime.now().isoformat()
            }
            
            # Check if already tracked
            existing_sources = [s for s in self.sources_by_level[level] if s.get("url") == url]
            if not existing_sources:
                self.sources_by_level[level].append(source_info)
                logger.debug(f"Tracked content source for {level.value}: {url}")
            
        except Exception as e:
            logger.error(f"Failed to track content source: {str(e)}")
    
    def get_field_source(self, level: ExtractionLevel, field_name: str, unit_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Get source information for a specific field."""
        field_key = f"{level.value}.{field_name}"
        if unit_id:
            field_key += f".unit_{unit_id}"
        
        return self.field_sources.get(field_key)
    
    def get_level_sources(self, level: ExtractionLevel) -> List[Dict[str, Any]]:
        """Get all sources used for a specific level."""
        return self.sources_by_level[level].copy()
    
    def get_all_sources(self) -> Dict[str, List[Dict[str, Any]]]:
        """Get all tracked sources organized by level."""
        return {
            level.value: sources for level, sources in self.sources_by_level.items()
        }
    
    def get_source_statistics(self) -> Dict[str, Any]:
        """Get statistics about tracked sources."""
        stats = {
            "session_id": self.extraction_session_id,
            "total_fields_tracked": len(self.field_sources),
            "levels": {},
            "source_types": {},
            "extraction_methods": {},
            "confidence_distribution": {
                "high": 0,    # >= 0.8
                "medium": 0,  # 0.5 - 0.8
                "low": 0      # < 0.5
            }
        }
        
        # Level statistics
        for level, sources in self.sources_by_level.items():
            unique_urls = set(s.get("url") for s in sources if s.get("url"))
            stats["levels"][level.value] = {
                "total_sources": len(sources),
                "unique_urls": len(unique_urls)
            }
        
        # Source type and extraction method statistics
        for field_info in self.field_sources.values():
            # Source types (from URLs)
            source_url = field_info.get("source_url", "")
            if source_url and source_url != "not_found":
                # Determine source type from URL
                source_type = self._determine_source_type_from_url(source_url)
                stats["source_types"][source_type] = stats["source_types"].get(source_type, 0) + 1
            
            # Extraction methods
            method = field_info.get("extraction_method", "unknown")
            stats["extraction_methods"][method] = stats["extraction_methods"].get(method, 0) + 1
            
            # Confidence distribution
            confidence = field_info.get("confidence_score", 0.0)
            if confidence >= 0.8:
                stats["confidence_distribution"]["high"] += 1
            elif confidence >= 0.5:
                stats["confidence_distribution"]["medium"] += 1
            else:
                stats["confidence_distribution"]["low"] += 1
        
        return stats
    
    def _determine_source_type_from_url(self, url: str) -> str:
        """Determine source type from URL."""
        url_lower = url.lower()
        
        if any(pattern in url_lower for pattern in ["company.com", "corp.com", "official"]):
            return "company_official"
        elif any(pattern in url_lower for pattern in ["gov.", "regulatory", "filing"]):
            return "government_regulatory"
        elif any(pattern in url_lower for pattern in ["wikipedia", "wiki"]):
            return "wikipedia"
        elif any(pattern in url_lower for pattern in ["news", "reuters", "bloomberg"]):
            return "news_article"
        else:
            return "other"
    
    def save_source_report(self, plant_name: str) -> str:
        """Save a comprehensive source report."""
        try:
            if not self.extraction_session_id:
                self.start_session(plant_name)
            
            report = {
                "session_id": self.extraction_session_id,
                "plant_name": plant_name,
                "generated_at": datetime.now().isoformat(),
                "statistics": self.get_source_statistics(),
                "field_sources": self.field_sources,
                "sources_by_level": {
                    level.value: sources for level, sources in self.sources_by_level.items()
                }
            }
            
            # Save report
            report_file = self.output_dir / f"source_report_{self.extraction_session_id}.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Saved source report: {report_file}")
            return str(report_file)
            
        except Exception as e:
            logger.error(f"Failed to save source report: {str(e)}")
            return ""
    
    def generate_field_attribution_summary(self, level: ExtractionLevel) -> Dict[str, Any]:
        """Generate a summary of field attribution for a level."""
        summary = {
            "level": level.value,
            "total_fields": 0,
            "fields_with_sources": 0,
            "fields_without_sources": 0,
            "average_confidence": 0.0,
            "field_details": []
        }
        
        level_fields = [
            field_key for field_key in self.field_sources.keys()
            if field_key.startswith(f"{level.value}.")
        ]
        
        summary["total_fields"] = len(level_fields)
        
        confidences = []
        
        for field_key in level_fields:
            field_info = self.field_sources[field_key]
            field_name = field_info.get("field_name", "")
            
            has_source = (
                field_info.get("source_url") and 
                field_info.get("source_url") != "not_found" and
                field_info.get("confidence_score", 0) > 0
            )
            
            if has_source:
                summary["fields_with_sources"] += 1
                confidences.append(field_info.get("confidence_score", 0))
            else:
                summary["fields_without_sources"] += 1
            
            summary["field_details"].append({
                "field_name": field_name,
                "has_source": has_source,
                "confidence": field_info.get("confidence_score", 0),
                "source_url": field_info.get("source_url", ""),
                "extraction_method": field_info.get("extraction_method", "")
            })
        
        if confidences:
            summary["average_confidence"] = sum(confidences) / len(confidences)
        
        return summary
    
    def export_sources_csv(self, plant_name: str) -> str:
        """Export source information to CSV format."""
        try:
            import csv
            
            if not self.extraction_session_id:
                self.start_session(plant_name)
            
            csv_file = self.output_dir / f"sources_{self.extraction_session_id}.csv"
            
            with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                
                # Write header
                writer.writerow([
                    "Level", "Field Name", "Unit ID", "Extracted Value", 
                    "Confidence Score", "Source URL", "Extraction Method", 
                    "Extraction Timestamp"
                ])
                
                # Write data
                for field_key, field_info in self.field_sources.items():
                    writer.writerow([
                        field_info.get("level", ""),
                        field_info.get("field_name", ""),
                        field_info.get("unit_id", ""),
                        str(field_info.get("extracted_value", ""))[:100],  # Truncate long values
                        field_info.get("confidence_score", 0),
                        field_info.get("source_url", ""),
                        field_info.get("extraction_method", ""),
                        field_info.get("extraction_timestamp", "")
                    ])
            
            logger.info(f"Exported sources to CSV: {csv_file}")
            return str(csv_file)
            
        except Exception as e:
            logger.error(f"Failed to export sources to CSV: {str(e)}")
            return ""
