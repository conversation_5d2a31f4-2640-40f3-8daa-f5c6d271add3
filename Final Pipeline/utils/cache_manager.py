"""
Cache manager for storing and retrieving data between extraction levels.
Enables hierarchical data flow and reduces redundant API calls.
"""

import json
import logging
import os
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from pathlib import Path

from ..core import CacheError, ExtractionLevel

logger = logging.getLogger(__name__)


class CacheManager:
    """Manages caching of extraction data between pipeline levels."""
    
    def __init__(self, cache_dir: str = "cache"):
        """Initialize the cache manager."""
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        
        # Cache expiration settings
        self.cache_expiry = {
            ExtractionLevel.ORGANIZATIONAL: timedelta(days=7),  # Org data changes slowly
            ExtractionLevel.PLANT: timedelta(days=3),           # Plant data moderately stable
            ExtractionLevel.UNIT: timedelta(days=1)             # Unit data changes more frequently
        }
        
        logger.info(f"Cache manager initialized with directory: {self.cache_dir}")
    
    def _get_cache_key(self, level: ExtractionLevel, plant_name: str, unit_id: Optional[str] = None) -> str:
        """Generate cache key for data storage."""
        # Normalize plant name for consistent caching
        normalized_name = plant_name.lower().replace(" ", "_").replace("-", "_")
        
        if level == ExtractionLevel.UNIT and unit_id:
            return f"{level.value}_{normalized_name}_unit_{unit_id}"
        else:
            return f"{level.value}_{normalized_name}"
    
    def _get_cache_file_path(self, cache_key: str) -> Path:
        """Get file path for cache key."""
        return self.cache_dir / f"{cache_key}.json"
    
    def store_data(
        self, 
        level: ExtractionLevel, 
        plant_name: str, 
        data: Dict[str, Any],
        unit_id: Optional[str] = None
    ) -> bool:
        """
        Store extraction data in cache.
        
        Args:
            level: Extraction level
            plant_name: Name of the power plant
            data: Data to cache
            unit_id: Optional unit identifier for unit-level data
            
        Returns:
            True if successfully stored
        """
        try:
            cache_key = self._get_cache_key(level, plant_name, unit_id)
            cache_file = self._get_cache_file_path(cache_key)
            
            # Prepare cache entry
            cache_entry = {
                "cache_key": cache_key,
                "level": level.value,
                "plant_name": plant_name,
                "unit_id": unit_id,
                "data": data,
                "cached_at": datetime.now().isoformat(),
                "expires_at": (datetime.now() + self.cache_expiry[level]).isoformat()
            }
            
            # Write to file
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_entry, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Cached {level.value} data for {plant_name} (key: {cache_key})")
            return True
            
        except Exception as e:
            logger.error(f"Failed to cache data: {str(e)}")
            raise CacheError(f"Cache storage failed: {str(e)}", cache_key=cache_key, operation="store")
    
    def retrieve_data(
        self, 
        level: ExtractionLevel, 
        plant_name: str,
        unit_id: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """
        Retrieve extraction data from cache.
        
        Args:
            level: Extraction level
            plant_name: Name of the power plant
            unit_id: Optional unit identifier for unit-level data
            
        Returns:
            Cached data if available and valid, None otherwise
        """
        try:
            cache_key = self._get_cache_key(level, plant_name, unit_id)
            cache_file = self._get_cache_file_path(cache_key)
            
            if not cache_file.exists():
                logger.debug(f"No cache found for {cache_key}")
                return None
            
            # Read cache entry
            with open(cache_file, 'r', encoding='utf-8') as f:
                cache_entry = json.load(f)
            
            # Check expiration
            expires_at = datetime.fromisoformat(cache_entry["expires_at"])
            if datetime.now() > expires_at:
                logger.info(f"Cache expired for {cache_key}, removing")
                cache_file.unlink()
                return None
            
            logger.info(f"Retrieved cached {level.value} data for {plant_name}")
            return cache_entry["data"]
            
        except Exception as e:
            logger.error(f"Failed to retrieve cached data: {str(e)}")
            return None
    
    def is_cached(
        self, 
        level: ExtractionLevel, 
        plant_name: str,
        unit_id: Optional[str] = None
    ) -> bool:
        """Check if data is available in cache."""
        return self.retrieve_data(level, plant_name, unit_id) is not None
    
    def invalidate_cache(
        self, 
        level: ExtractionLevel, 
        plant_name: str,
        unit_id: Optional[str] = None
    ) -> bool:
        """
        Invalidate cached data.
        
        Args:
            level: Extraction level
            plant_name: Name of the power plant
            unit_id: Optional unit identifier
            
        Returns:
            True if cache was invalidated
        """
        try:
            cache_key = self._get_cache_key(level, plant_name, unit_id)
            cache_file = self._get_cache_file_path(cache_key)
            
            if cache_file.exists():
                cache_file.unlink()
                logger.info(f"Invalidated cache for {cache_key}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to invalidate cache: {str(e)}")
            return False
    
    def clear_all_cache(self) -> int:
        """
        Clear all cached data.
        
        Returns:
            Number of cache files removed
        """
        try:
            cache_files = list(self.cache_dir.glob("*.json"))
            count = 0
            
            for cache_file in cache_files:
                try:
                    cache_file.unlink()
                    count += 1
                except Exception as e:
                    logger.error(f"Failed to remove cache file {cache_file}: {str(e)}")
            
            logger.info(f"Cleared {count} cache files")
            return count
            
        except Exception as e:
            logger.error(f"Failed to clear cache: {str(e)}")
            return 0
    
    def cleanup_expired_cache(self) -> int:
        """
        Remove expired cache entries.
        
        Returns:
            Number of expired cache files removed
        """
        try:
            cache_files = list(self.cache_dir.glob("*.json"))
            removed_count = 0
            
            for cache_file in cache_files:
                try:
                    with open(cache_file, 'r', encoding='utf-8') as f:
                        cache_entry = json.load(f)
                    
                    expires_at = datetime.fromisoformat(cache_entry["expires_at"])
                    if datetime.now() > expires_at:
                        cache_file.unlink()
                        removed_count += 1
                        logger.debug(f"Removed expired cache: {cache_file.name}")
                        
                except Exception as e:
                    logger.error(f"Error processing cache file {cache_file}: {str(e)}")
                    continue
            
            if removed_count > 0:
                logger.info(f"Cleaned up {removed_count} expired cache files")
            
            return removed_count
            
        except Exception as e:
            logger.error(f"Failed to cleanup expired cache: {str(e)}")
            return 0
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        try:
            cache_files = list(self.cache_dir.glob("*.json"))
            
            stats = {
                "total_files": len(cache_files),
                "cache_dir": str(self.cache_dir),
                "levels": {level.value: 0 for level in ExtractionLevel},
                "expired_files": 0,
                "total_size_mb": 0.0
            }
            
            total_size = 0
            
            for cache_file in cache_files:
                try:
                    # Get file size
                    file_size = cache_file.stat().st_size
                    total_size += file_size
                    
                    # Read cache entry to get level
                    with open(cache_file, 'r', encoding='utf-8') as f:
                        cache_entry = json.load(f)
                    
                    level = cache_entry.get("level", "unknown")
                    if level in stats["levels"]:
                        stats["levels"][level] += 1
                    
                    # Check if expired
                    expires_at = datetime.fromisoformat(cache_entry["expires_at"])
                    if datetime.now() > expires_at:
                        stats["expired_files"] += 1
                        
                except Exception as e:
                    logger.error(f"Error reading cache file {cache_file}: {str(e)}")
                    continue
            
            stats["total_size_mb"] = round(total_size / (1024 * 1024), 2)
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get cache stats: {str(e)}")
            return {"error": str(e)}
    
    def get_cached_plants(self) -> List[str]:
        """Get list of plants that have cached data."""
        try:
            cache_files = list(self.cache_dir.glob("*.json"))
            plants = set()
            
            for cache_file in cache_files:
                try:
                    with open(cache_file, 'r', encoding='utf-8') as f:
                        cache_entry = json.load(f)
                    
                    plant_name = cache_entry.get("plant_name")
                    if plant_name:
                        plants.add(plant_name)
                        
                except Exception as e:
                    logger.error(f"Error reading cache file {cache_file}: {str(e)}")
                    continue
            
            return sorted(list(plants))
            
        except Exception as e:
            logger.error(f"Failed to get cached plants: {str(e)}")
            return []
