"""
Main pipeline orchestrator that coordinates the complete hierarchical extraction process.
Manages the flow from organizational to plant to unit level extraction.
"""

import asyncio
import logging
import time
from typing import Dict, List, Any, Optional
from datetime import datetime

from ..core import (
    config, ExtractionLevel, ExtractionSession, PipelineResult,
    PipelineException
)
from ..clients import OpenAIExtractionClient, ScraperAPIClient, SearchAPIClient
from ..processors import SchemaProcessor, DataValidator, VisionProcessor
from ..extractors import OrganizationalExtractor, PlantExtractor, UnitExtractor
from ..utils import CacheManager, SourceTracker, FieldAnalyzer

logger = logging.getLogger(__name__)


class PipelineOrchestrator:
    """Main orchestrator for the hierarchical power plant extraction pipeline."""
    
    def __init__(self):
        """Initialize the pipeline orchestrator."""
        self.session: Optional[ExtractionSession] = None
        
        # Initialize core components
        self.schema_processor = SchemaProcessor()
        self.data_validator = DataValidator(self.schema_processor)
        self.cache_manager = CacheManager()
        self.source_tracker = SourceTracker()
        self.field_analyzer = FieldAnalyzer(self.schema_processor)
        
        # Initialize API clients
        self.openai_client = OpenAIExtractionClient()
        self.search_client = SearchAPIClient()
        
        # Initialize extractors (will be created with scraper client in context)
        self._extractors_initialized = False
        
        logger.info("Pipeline orchestrator initialized")
    
    async def extract_complete_hierarchy(self, plant_name: str) -> PipelineResult:
        """
        Extract complete hierarchical data for a power plant.
        
        Args:
            plant_name: Name of the power plant to extract data for
            
        Returns:
            PipelineResult with all extracted data and metadata
        """
        start_time = time.time()
        session_id = f"{plant_name.replace(' ', '_')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        try:
            logger.info(f"🚀 Starting complete hierarchical extraction for: {plant_name}")
            
            # Initialize session
            self.session = ExtractionSession(
                plant_name=plant_name,
                session_id=session_id
            )
            
            # Start source tracking
            self.source_tracker.start_session(plant_name)
            
            # Initialize extractors with scraper client
            async with ScraperAPIClient() as scraper_client:
                await self._initialize_extractors(scraper_client)
                
                # Phase 1: Organizational extraction
                logger.info("📋 Phase 1: Organizational Level Extraction")
                org_data = await self._extract_organizational_level(plant_name)
                self.session.org_data = org_data
                self.session.mark_level_complete(ExtractionLevel.ORGANIZATIONAL)
                
                # Phase 2: Plant extraction
                logger.info("🏭 Phase 2: Plant Level Extraction")
                plant_data = await self._extract_plant_level(plant_name, org_data)
                self.session.plant_data = plant_data
                self.session.mark_level_complete(ExtractionLevel.PLANT)
                
                # Phase 3: Unit extraction
                logger.info("⚡ Phase 3: Unit Level Extraction")
                unit_data = await self._extract_unit_level(plant_name, org_data, plant_data)
                self.session.unit_data = unit_data
                self.session.mark_level_complete(ExtractionLevel.UNIT)
            
            # Calculate execution time
            execution_time = time.time() - start_time
            self.session.end_time = datetime.now()
            
            # Generate final result
            result = await self._generate_pipeline_result(execution_time)
            
            # Save source report
            self.source_tracker.save_source_report(plant_name)
            
            logger.info(f"✅ Complete extraction finished in {execution_time:.2f} seconds")
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"❌ Pipeline extraction failed: {str(e)}")
            
            return PipelineResult(
                plant_name=plant_name,
                session_id=session_id,
                execution_time=execution_time,
                success=False,
                organizational_data={},
                plant_data={},
                unit_data=[],
                total_fields_extracted=0,
                total_sources_used=0,
                extraction_stats={},
                error_message=str(e)
            )
    
    async def _initialize_extractors(self, scraper_client: ScraperAPIClient):
        """Initialize extractors with shared clients."""
        if self._extractors_initialized:
            return
        
        # Create extractors with shared clients
        self.org_extractor = OrganizationalExtractor(
            self.openai_client,
            scraper_client,
            self.search_client,
            self.schema_processor,
            self.data_validator
        )
        
        self.plant_extractor = PlantExtractor(
            self.openai_client,
            scraper_client,
            self.search_client,
            self.schema_processor,
            self.data_validator
        )
        
        self.unit_extractor = UnitExtractor(
            self.openai_client,
            scraper_client,
            self.search_client,
            self.schema_processor,
            self.data_validator
        )
        
        self._extractors_initialized = True
        logger.info("Extractors initialized with shared clients")
    
    async def _extract_organizational_level(self, plant_name: str) -> Dict[str, Any]:
        """Extract organizational level data."""
        try:
            # Check cache first
            if config.extraction.enable_caching:
                cached_data = self.cache_manager.retrieve_data(
                    ExtractionLevel.ORGANIZATIONAL, plant_name
                )
                if cached_data:
                    logger.info("Using cached organizational data")
                    return cached_data
            
            # Extract organizational data
            org_data = await self.org_extractor.extract_organizational_data(plant_name)
            
            # Cache the result
            if config.extraction.enable_caching:
                self.cache_manager.store_data(
                    ExtractionLevel.ORGANIZATIONAL, plant_name, org_data
                )
            
            # Track field sources
            if config.extraction.enable_source_tracking:
                self._track_extraction_sources(
                    ExtractionLevel.ORGANIZATIONAL, org_data, plant_name
                )
            
            return org_data
            
        except Exception as e:
            logger.error(f"Organizational extraction failed: {str(e)}")
            raise PipelineException(f"Organizational extraction failed: {str(e)}")
    
    async def _extract_plant_level(
        self, 
        plant_name: str, 
        org_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Extract plant level data."""
        try:
            # Check cache first
            if config.extraction.enable_caching:
                cached_data = self.cache_manager.retrieve_data(
                    ExtractionLevel.PLANT, plant_name
                )
                if cached_data:
                    logger.info("Using cached plant data")
                    return cached_data
            
            # Extract plant data
            plant_data = await self.plant_extractor.extract_plant_data(
                plant_name, org_context
            )
            
            # Cache the result
            if config.extraction.enable_caching:
                self.cache_manager.store_data(
                    ExtractionLevel.PLANT, plant_name, plant_data
                )
            
            # Track field sources
            if config.extraction.enable_source_tracking:
                self._track_extraction_sources(
                    ExtractionLevel.PLANT, plant_data, plant_name
                )
            
            return plant_data
            
        except Exception as e:
            logger.error(f"Plant extraction failed: {str(e)}")
            raise PipelineException(f"Plant extraction failed: {str(e)}")
    
    async def _extract_unit_level(
        self,
        plant_name: str,
        org_context: Dict[str, Any],
        plant_context: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Extract unit level data."""
        try:
            # Get unit IDs from plant data
            unit_ids = plant_context.get("units_id", ["1"])
            logger.info(f"Extracting data for {len(unit_ids)} units: {unit_ids}")
            
            all_unit_data = []
            
            for unit_id in unit_ids:
                # Check cache first
                cached_unit_data = None
                if config.extraction.enable_caching:
                    cached_unit_data = self.cache_manager.retrieve_data(
                        ExtractionLevel.UNIT, plant_name, unit_id
                    )
                
                if cached_unit_data:
                    logger.info(f"Using cached data for Unit {unit_id}")
                    all_unit_data.append(cached_unit_data)
                else:
                    # Extract unit data
                    unit_data = await self.unit_extractor._extract_single_unit_data(
                        plant_name, unit_id, org_context, plant_context
                    )
                    
                    # Cache the result
                    if config.extraction.enable_caching:
                        self.cache_manager.store_data(
                            ExtractionLevel.UNIT, plant_name, unit_data, unit_id
                        )
                    
                    # Track field sources
                    if config.extraction.enable_source_tracking:
                        self._track_extraction_sources(
                            ExtractionLevel.UNIT, unit_data, plant_name, unit_id
                        )
                    
                    all_unit_data.append(unit_data)
            
            return all_unit_data
            
        except Exception as e:
            logger.error(f"Unit extraction failed: {str(e)}")
            raise PipelineException(f"Unit extraction failed: {str(e)}")
    
    def _track_extraction_sources(
        self,
        level: ExtractionLevel,
        data: Dict[str, Any],
        plant_name: str,
        unit_id: Optional[str] = None
    ):
        """Track sources for extracted fields."""
        try:
            field_sources = data.get("_field_sources", {})
            
            for field_name, source_info in field_sources.items():
                # Create mock extraction result for tracking
                from ..core import ExtractionResult
                
                result = ExtractionResult(
                    field_name=field_name,
                    extracted_value=data.get(field_name),
                    confidence_score=source_info.get("confidence", 0.0),
                    source_url=source_info.get("source_url", ""),
                    extraction_method=source_info.get("extraction_method", "")
                )
                
                self.source_tracker.track_field_extraction(
                    level, field_name, result, plant_name, unit_id
                )
                
        except Exception as e:
            logger.error(f"Failed to track extraction sources: {str(e)}")
    
    async def _generate_pipeline_result(self, execution_time: float) -> PipelineResult:
        """Generate the final pipeline result."""
        try:
            # Count total fields extracted
            total_fields = 0
            
            # Count org fields
            org_fields = sum(
                1 for value in self.session.org_data.values()
                if value is not None and value != ""
            )
            total_fields += org_fields
            
            # Count plant fields
            plant_fields = sum(
                1 for value in self.session.plant_data.values()
                if value is not None and value != ""
            )
            total_fields += plant_fields
            
            # Count unit fields
            for unit_data in self.session.unit_data:
                unit_fields = sum(
                    1 for value in unit_data.values()
                    if value is not None and value != ""
                )
                total_fields += unit_fields
            
            # Get source statistics
            source_stats = self.source_tracker.get_source_statistics()
            total_sources = len(set(
                source.get("url", "") for sources in self.source_tracker.get_all_sources().values()
                for source in sources if source.get("url")
            ))
            
            # Generate extraction statistics
            extraction_stats = {
                "session_id": self.session.session_id,
                "extraction_duration": execution_time,
                "levels_completed": {
                    "organizational": self.session.org_completed,
                    "plant": self.session.plant_completed,
                    "unit": self.session.unit_completed
                },
                "completion_percentage": self.session.get_completion_percentage(),
                "source_statistics": source_stats,
                "openai_usage": self.openai_client.get_usage_stats()
            }
            
            return PipelineResult(
                plant_name=self.session.plant_name,
                session_id=self.session.session_id,
                execution_time=execution_time,
                success=self.session.is_complete(),
                organizational_data=self.session.org_data,
                plant_data=self.session.plant_data,
                unit_data=self.session.unit_data,
                total_fields_extracted=total_fields,
                total_sources_used=total_sources,
                extraction_stats=extraction_stats
            )
            
        except Exception as e:
            logger.error(f"Failed to generate pipeline result: {str(e)}")
            raise PipelineException(f"Result generation failed: {str(e)}")
    
    def get_extraction_progress(self) -> Dict[str, Any]:
        """Get current extraction progress."""
        if not self.session:
            return {"status": "not_started"}
        
        return {
            "status": "in_progress" if not self.session.is_complete() else "completed",
            "plant_name": self.session.plant_name,
            "session_id": self.session.session_id,
            "current_level": self.session.current_level.value if self.session.current_level else None,
            "completion_percentage": self.session.get_completion_percentage(),
            "levels_status": {
                "organizational": self.session.org_completed,
                "plant": self.session.plant_completed,
                "unit": self.session.unit_completed
            }
        }
