# 🚀 Universal Hierarchical Power Plant Extraction Pipeline

A comprehensive, schema-driven pipeline for extracting complete power plant data using AI and web scraping. Extracts organizational, plant, and unit-level information for any power plant globally.

## 🎯 Features

- **🔄 Hierarchical Extraction**: Org → Plant → Unit level data extraction
- **🧠 AI-Powered**: Uses OpenAI GPT-4o-mini with vision capabilities
- **📄 Vision Processing**: Handles scanned PDFs and image documents
- **🌐 Universal**: Works for any power plant globally (no hardcoding)
- **📊 Schema-Compliant**: Extracts ALL fields from comprehensive schemas
- **🔍 Source Attribution**: Complete tracking of data sources and confidence
- **💾 Smart Caching**: Hierarchical data flow with intelligent caching
- **⚡ Async Processing**: High-performance concurrent operations

## 📋 Schema Coverage

### Organizational Level (11 fields)
- Company ownership and structure
- Financial year and currency information
- Plant portfolio and PPA structure

### Plant Level (7 main + nested fields)
- Location and technical specifications
- Grid connectivity maps with substation details
- Power Purchase Agreement (PPA) details with respondents
- Operational units list

### Unit Level (30+ technical fields)
- Detailed technical specifications
- Efficiency and performance metrics
- Fuel consumption and emissions data
- Financial and operational parameters

## 🛠️ Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd "Final Pipeline"
```

2. **Install dependencies**
```bash
pip install -r requirements.txt
```

3. **Set up environment variables**
Create a `.env` file in the root directory:
```env
# API Keys
SCRAPER_API_KEY=your_scraper_api_key
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4o-mini

# Pipeline Configuration
MAX_SEARCH_RESULTS=10
MAX_SCRAPE_PAGES=5
CONFIDENCE_THRESHOLD=0.7
```

## 🚀 Quick Start

### Command Line Usage
```bash
python main.py "Jhajjar Power Plant"
```

### Python API Usage
```python
import asyncio
from main import UniversalPowerPlantPipeline

async def extract_plant():
    pipeline = UniversalPowerPlantPipeline()
    result = await pipeline.extract_plant_data("Jhajjar Power Plant")
    
    if result.success:
        print(f"Extracted {result.total_fields_extracted} fields")
        print(f"Used {result.total_sources_used} sources")
    else:
        print(f"Extraction failed: {result.error_message}")

asyncio.run(extract_plant())
```

## 📁 Project Structure

```
Final Pipeline/
├── core/                    # Core configuration and models
│   ├── config.py           # Environment and pipeline settings
│   ├── models.py           # Data models and structures
│   └── exceptions.py       # Custom exception handling
├── clients/                 # API clients
│   ├── scraper_client.py   # Web scraping with PDF support
│   ├── openai_client.py    # OpenAI with vision capabilities
│   └── search_client.py    # Search orchestration
├── processors/             # Data processing
│   ├── schema_processor.py # Schema parsing and analysis
│   ├── data_validator.py   # Data validation and cleaning
│   └── vision_processor.py # Vision-enhanced document processing
├── extractors/             # Level-specific extractors
│   ├── base_extractor.py   # Common extraction patterns
│   ├── org_extractor.py    # Organizational data extraction
│   ├── plant_extractor.py  # Plant data extraction
│   └── unit_extractor.py   # Unit data extraction
├── orchestrators/          # Pipeline coordination
│   └── pipeline_orchestrator.py # Main pipeline controller
├── utils/                  # Utilities
│   ├── cache_manager.py    # Data caching between levels
│   ├── source_tracker.py   # Source attribution tracking
│   └── field_analyzer.py   # Field completion analysis
├── schemas/                # JSON schemas
│   ├── org_level.json      # Organizational schema
│   ├── plant_level.json    # Plant schema
│   └── unit_level.json     # Unit schema
├── main.py                 # Main entry point
└── requirements.txt        # Dependencies
```

## 🔧 Configuration

### API Configuration
- **Scraper API**: For web search and scraping
- **OpenAI API**: For AI-powered data extraction with vision

### Pipeline Settings
- **Vision Processing**: Enable/disable vision capabilities
- **Caching**: Smart caching for hierarchical data flow
- **Source Tracking**: Complete data lineage tracking
- **Rate Limiting**: Configurable delays and retry mechanisms

## 📊 Output Format

The pipeline generates comprehensive JSON outputs:

### Complete Result
```json
{
  "plant_name": "Jhajjar Power Plant",
  "session_id": "jhajjar_power_plant_20240101_120000",
  "success": true,
  "execution_time": 45.2,
  "organizational_data": { ... },
  "plant_data": { ... },
  "unit_data": [ ... ],
  "metadata": {
    "total_fields_extracted": 87,
    "total_sources_used": 15,
    "extraction_stats": { ... }
  }
}
```

### Individual Level Files
- `{plant}_organizational_{timestamp}.json`
- `{plant}_plant_{timestamp}.json`
- `{plant}_units_{timestamp}.json`
- `source_report_{session_id}.json`

## 🎯 Key Features

### Schema-Driven Extraction
- Uses field descriptions for intelligent search queries
- Dynamic prompt generation based on field semantics
- No hardcoded values or plant-specific logic

### Vision-Enhanced Processing
- Automatic detection of scanned vs text PDFs
- GPT-4o-mini vision processing for image documents
- Multi-page document analysis

### Hierarchical Data Flow
- Organizational context enhances plant extraction
- Plant context enhances unit extraction
- Smart caching reduces redundant API calls

### Source Attribution
- Complete tracking of data sources
- Confidence scoring for all extractions
- Detailed extraction method logging

## 🌍 Global Compatibility

The pipeline is designed to work with any power plant worldwide:

- **No Hardcoding**: All extraction based on dynamic search
- **Country-Specific**: Handles different financial years and currencies
- **Multi-Language**: Processes content in various languages
- **Universal Schemas**: Comprehensive field coverage for all plant types

## 📈 Performance

- **Concurrent Processing**: Async operations for optimal speed
- **Smart Caching**: Reduces redundant extractions
- **Rate Limiting**: Respects API limits and prevents blocking
- **Error Recovery**: Robust error handling and retry mechanisms

## 🔍 Monitoring and Analytics

### Extraction Statistics
- Field completion rates
- Source quality metrics
- Confidence score distributions
- Performance timing analysis

### Source Reports
- Complete data lineage
- Source type classification
- Extraction method tracking
- Quality assessment

## 🚨 Error Handling

- Comprehensive exception hierarchy
- Graceful degradation on failures
- Detailed error logging and reporting
- Automatic retry mechanisms

## 📝 Examples

### Extract Multiple Plants
```python
plants = ["Jhajjar Power Plant", "Mundra Power Plant", "Adani Godda"]

for plant in plants:
    result = await pipeline.extract_plant_data(plant)
    print(f"{plant}: {result.total_fields_extracted} fields extracted")
```

### Cache Management
```python
# Clear cache
pipeline.clear_cache()

# Get cache statistics
stats = pipeline.get_cache_stats()
print(f"Cache size: {stats['total_size_mb']} MB")
```

### Schema Analysis
```python
# Get schema summary
summary = pipeline.get_schema_summary()
for level, info in summary.items():
    print(f"{level}: {info['total_fields']} fields")
```

## 🤝 Contributing

1. Follow the modular architecture
2. Add comprehensive error handling
3. Include source attribution
4. Write tests for new features
5. Update documentation

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For issues and questions:
1. Check the logs in `pipeline.log`
2. Review the source reports for data quality
3. Verify API key configuration
4. Check network connectivity

---

**Built with ❤️ for universal power plant data extraction**
