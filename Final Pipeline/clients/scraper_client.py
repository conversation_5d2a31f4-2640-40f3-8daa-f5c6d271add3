"""
Scraper API client for web scraping and content extraction.
Handles both HTML and PDF content with automatic type detection.
"""

import asyncio
import aiohttp
import logging
from typing import Optional, List, Dict, Any
from urllib.parse import urlparse
import PyPDF2
import io
from tenacity import retry, stop_after_attempt, wait_exponential

from ..core import (
    config, ScrapedContent, DocumentType, SourceType, 
    ScraperAPIError, ContentProcessingError, handle_api_error
)

logger = logging.getLogger(__name__)


class ScraperAPIClient:
    """Client for web scraping using Scraper API with PDF support."""
    
    def __init__(self, api_key: Optional[str] = None):
        """Initialize the Scraper API client."""
        self.api_key = api_key or config.api.scraper_api_key
        self.base_url = "http://api.scraperapi.com"
        self.session: Optional[aiohttp.ClientSession] = None
        
        if not self.api_key:
            raise ScraperAPIError("Scraper API key is required")
        
        logger.info("Scraper API client initialized")
    
    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=config.pipeline.request_timeout)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    @handle_api_error
    async def scrape_url(self, url: str, render_js: bool = False) -> Optional[ScrapedContent]:
        """
        Scrape content from a URL using Scraper API.
        
        Args:
            url: URL to scrape
            render_js: Whether to render JavaScript
            
        Returns:
            ScrapedContent object or None if failed
        """
        if not self.session:
            raise ScraperAPIError("ScraperAPIClient must be used as async context manager")
        
        params = {
            "api_key": self.api_key,
            "url": url,
            "render": "true" if render_js else "false",
            "country_code": "us"
        }
        
        try:
            logger.info(f"Scraping URL: {url}")
            
            async with self.session.get(self.base_url, params=params) as response:
                if response.status == 200:
                    content_type = response.headers.get('content-type', '').lower()
                    
                    # Determine document type
                    if self._is_pdf_content(content_type, url):
                        pdf_bytes = await response.read()
                        return await self._process_pdf_content(url, pdf_bytes)
                    else:
                        html_content = await response.text()
                        return self._process_html_content(url, html_content)
                        
                elif response.status == 429:
                    raise ScraperAPIError("Rate limit exceeded", status_code=429, url=url)
                else:
                    logger.warning(f"Failed to scrape {url}: HTTP {response.status}")
                    return None
                    
        except aiohttp.ClientError as e:
            raise ScraperAPIError(f"Network error while scraping {url}: {str(e)}", url=url)
        except Exception as e:
            logger.error(f"Unexpected error scraping {url}: {str(e)}")
            return None
    
    def _is_pdf_content(self, content_type: str, url: str) -> bool:
        """Check if content is PDF based on content type or URL."""
        return (
            'application/pdf' in content_type or
            url.lower().endswith('.pdf') or
            'pdf' in content_type
        )
    
    async def _process_pdf_content(self, url: str, pdf_bytes: bytes) -> Optional[ScrapedContent]:
        """Process PDF content and extract text."""
        try:
            # Determine if PDF is scanned or text-based
            pdf_file = io.BytesIO(pdf_bytes)
            pdf_reader = PyPDF2.PdfReader(pdf_file)
            
            text_content = ""
            page_count = len(pdf_reader.pages)
            
            # Try to extract text from first few pages
            for i, page in enumerate(pdf_reader.pages[:5]):  # Limit to first 5 pages
                try:
                    page_text = page.extract_text()
                    text_content += page_text + "\n"
                except Exception as e:
                    logger.warning(f"Failed to extract text from page {i+1}: {str(e)}")
            
            # Determine document type based on extracted text
            if len(text_content.strip()) < 100:
                # Likely a scanned PDF - will need vision processing
                document_type = DocumentType.PDF_SCANNED
                content = f"[SCANNED PDF - {page_count} pages] Requires vision processing for text extraction."
            else:
                # Text-based PDF
                document_type = DocumentType.PDF_TEXT
                content = text_content.strip()
            
            # Determine source type
            source_type = self._determine_source_type(url)
            
            return ScrapedContent(
                url=url,
                content=content,
                title=self._extract_title_from_url(url),
                document_type=document_type,
                source_type=source_type,
                is_pdf=True,
                pdf_pages=page_count
            )
            
        except Exception as e:
            raise ContentProcessingError(
                f"Failed to process PDF content: {str(e)}",
                url=url,
                content_type="application/pdf",
                content_length=len(pdf_bytes)
            )
    
    def _process_html_content(self, url: str, html_content: str) -> Optional[ScrapedContent]:
        """Process HTML content and extract text."""
        try:
            # Basic HTML cleaning (remove scripts, styles, etc.)
            cleaned_content = self._clean_html_content(html_content)
            
            if len(cleaned_content) < config.pipeline.min_content_length:
                logger.warning(f"Content too short for {url}: {len(cleaned_content)} chars")
                return None
            
            # Extract title
            title = self._extract_title_from_html(html_content) or self._extract_title_from_url(url)
            
            # Determine source type
            source_type = self._determine_source_type(url)
            
            return ScrapedContent(
                url=url,
                content=cleaned_content,
                title=title,
                document_type=DocumentType.HTML,
                source_type=source_type,
                is_pdf=False
            )
            
        except Exception as e:
            raise ContentProcessingError(
                f"Failed to process HTML content: {str(e)}",
                url=url,
                content_type="text/html",
                content_length=len(html_content)
            )
    
    def _clean_html_content(self, html_content: str) -> str:
        """Clean HTML content and extract readable text."""
        try:
            from bs4 import BeautifulSoup
            
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Remove script and style elements
            for script in soup(["script", "style", "nav", "header", "footer"]):
                script.decompose()
            
            # Get text content
            text = soup.get_text()
            
            # Clean up whitespace
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = ' '.join(chunk for chunk in chunks if chunk)
            
            return text
            
        except ImportError:
            # Fallback if BeautifulSoup is not available
            logger.warning("BeautifulSoup not available, using basic HTML cleaning")
            return self._basic_html_clean(html_content)
        except Exception as e:
            logger.warning(f"HTML cleaning failed: {str(e)}, using basic cleaning")
            return self._basic_html_clean(html_content)
    
    def _basic_html_clean(self, html_content: str) -> str:
        """Basic HTML cleaning without external dependencies."""
        import re
        
        # Remove HTML tags
        text = re.sub(r'<[^>]+>', ' ', html_content)
        
        # Clean up whitespace
        text = re.sub(r'\s+', ' ', text)
        
        return text.strip()
    
    def _extract_title_from_html(self, html_content: str) -> Optional[str]:
        """Extract title from HTML content."""
        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html_content, 'html.parser')
            title_tag = soup.find('title')
            return title_tag.get_text().strip() if title_tag else None
        except:
            # Fallback regex extraction
            import re
            match = re.search(r'<title[^>]*>([^<]+)</title>', html_content, re.IGNORECASE)
            return match.group(1).strip() if match else None
    
    def _extract_title_from_url(self, url: str) -> str:
        """Extract a title from URL as fallback."""
        try:
            parsed = urlparse(url)
            path = parsed.path.strip('/')
            if path:
                return path.replace('/', ' - ').replace('-', ' ').replace('_', ' ').title()
            else:
                return parsed.netloc.replace('www.', '').title()
        except:
            return "Unknown Document"
    
    def _determine_source_type(self, url: str) -> SourceType:
        """Determine source type based on URL patterns."""
        url_lower = url.lower()
        
        for source_type, patterns in config.source_type_patterns.items():
            if any(pattern in url_lower for pattern in patterns):
                return SourceType(source_type)
        
        return SourceType.OTHER
