"""
Search client for web search operations using Scraper API's structured search.
Provides comprehensive search capabilities for power plant information.
"""

import asyncio
import aiohttp
import logging
from typing import List, Dict, Optional, Any
from urllib.parse import quote_plus
from tenacity import retry, stop_after_attempt, wait_exponential

from ..core import (
    config, SearchResult, SourceType, 
    SearchError, handle_api_error
)

logger = logging.getLogger(__name__)


class SearchAPIClient:
    """Client for web search using Scraper API's structured search."""
    
    def __init__(self, api_key: Optional[str] = None):
        """Initialize the search client."""
        self.api_key = api_key or config.api.scraper_api_key
        self.base_url = "https://api.scraperapi.com/structured/google/search"
        self.session: Optional[aiohttp.ClientSession] = None
        
        if not self.api_key:
            raise SearchError("Scraper API key is required for search operations")
        
        logger.info("Search API client initialized")
    
    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=config.pipeline.request_timeout)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    @handle_api_error
    async def search(
        self,
        query: str,
        num_results: int = None,
        search_type: str = "general"
    ) -> List[SearchResult]:
        """
        Perform web search using Scraper API.
        
        Args:
            query: Search query string
            num_results: Number of results to return
            search_type: Type of search for tracking
            
        Returns:
            List of SearchResult objects
        """
        if not self.session:
            raise SearchError("SearchAPIClient must be used as async context manager")
        
        num_results = num_results or config.pipeline.max_search_results
        
        params = {
            "api_key": self.api_key,
            "query": query,
            "num": num_results,
            "page": 1
        }
        
        try:
            logger.info(f"Searching: {query} (expecting {num_results} results)")
            
            async with self.session.get(self.base_url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._parse_search_results(data, query)
                elif response.status == 429:
                    raise SearchError("Search rate limit exceeded", query=query, search_type=search_type)
                else:
                    logger.warning(f"Search failed for '{query}': HTTP {response.status}")
                    return []
                    
        except aiohttp.ClientError as e:
            raise SearchError(f"Network error during search: {str(e)}", query=query, search_type=search_type)
        except Exception as e:
            logger.error(f"Unexpected error during search '{query}': {str(e)}")
            return []
    
    def _parse_search_results(self, data: Dict[str, Any], query: str) -> List[SearchResult]:
        """Parse search results from API response."""
        results = []
        
        try:
            # Handle different response formats
            organic_results = data.get('organic_results', [])
            if not organic_results:
                organic_results = data.get('results', [])
            
            for i, result in enumerate(organic_results):
                try:
                    # Extract basic information
                    title = result.get('title', '').strip()
                    url = result.get('link', result.get('url', '')).strip()
                    snippet = result.get('snippet', result.get('description', '')).strip()
                    
                    if not url or not title:
                        continue
                    
                    # Determine source type
                    source_type = self._determine_source_type(url)
                    
                    search_result = SearchResult(
                        title=title,
                        url=url,
                        snippet=snippet,
                        rank=i + 1,
                        source_type=source_type
                    )
                    
                    results.append(search_result)
                    
                except Exception as e:
                    logger.warning(f"Failed to parse search result {i}: {str(e)}")
                    continue
            
            logger.info(f"Parsed {len(results)} search results for query: {query}")
            return results
            
        except Exception as e:
            logger.error(f"Failed to parse search results: {str(e)}")
            return []
    
    def _determine_source_type(self, url: str) -> SourceType:
        """Determine source type based on URL patterns."""
        url_lower = url.lower()
        
        for source_type, patterns in config.source_type_patterns.items():
            if any(pattern in url_lower for pattern in patterns):
                return SourceType(source_type)
        
        return SourceType.OTHER
    
    async def multi_query_search(
        self,
        queries: List[str],
        max_results_per_query: int = None
    ) -> Dict[str, List[SearchResult]]:
        """
        Perform multiple searches concurrently.
        
        Args:
            queries: List of search queries
            max_results_per_query: Maximum results per query
            
        Returns:
            Dictionary mapping query -> search results
        """
        max_results_per_query = max_results_per_query or config.pipeline.max_search_results
        
        # Create search tasks
        tasks = [
            self.search(query, max_results_per_query, "multi_query")
            for query in queries
        ]
        
        # Execute searches with rate limiting
        results = {}
        for i, (query, task) in enumerate(zip(queries, tasks)):
            try:
                search_results = await task
                results[query] = search_results
                
                # Rate limiting between searches
                if i < len(tasks) - 1:
                    await asyncio.sleep(config.pipeline.search_delay)
                    
            except Exception as e:
                logger.error(f"Search failed for query '{query}': {str(e)}")
                results[query] = []
        
        return results


class PowerPlantSearchOrchestrator:
    """Orchestrates comprehensive search strategies for power plant data."""
    
    def __init__(self, search_client: SearchAPIClient):
        """Initialize the search orchestrator."""
        self.search_client = search_client
    
    async def comprehensive_search(self, plant_name: str) -> Dict[str, List[SearchResult]]:
        """
        Perform comprehensive multi-stage search for power plant information.
        
        Args:
            plant_name: Name of the power plant
            
        Returns:
            Dictionary with categorized search results
        """
        all_results = {}
        
        # Stage 1: Basic discovery
        logger.info(f"Stage 1: Basic discovery search for {plant_name}")
        basic_queries = [
            f"{plant_name} power plant",
            f"{plant_name} power station",
            f"{plant_name} generating facility"
        ]
        basic_results = await self.search_client.multi_query_search(basic_queries)
        all_results["basic_discovery"] = self._flatten_results(basic_results)
        
        # Stage 2: Organizational search
        logger.info(f"Stage 2: Organizational search for {plant_name}")
        org_queries = [
            template.format(plant_name=plant_name)
            for template in config.extraction.search_query_templates["organizational"]
        ]
        org_results = await self.search_client.multi_query_search(org_queries)
        all_results["organizational"] = self._flatten_results(org_results)
        
        # Stage 3: Technical details search
        logger.info(f"Stage 3: Technical details search for {plant_name}")
        tech_queries = [
            template.format(plant_name=plant_name)
            for template in config.extraction.search_query_templates["plant_technical"]
        ]
        tech_results = await self.search_client.multi_query_search(tech_queries)
        all_results["technical"] = self._flatten_results(tech_results)
        
        return all_results
    
    async def unit_specific_search(self, plant_name: str, unit_id: str) -> List[SearchResult]:
        """
        Perform unit-specific search for detailed technical information.
        
        Args:
            plant_name: Name of the power plant
            unit_id: Specific unit identifier
            
        Returns:
            List of search results for the specific unit
        """
        logger.info(f"Unit-specific search for {plant_name} Unit {unit_id}")
        
        unit_queries = [
            template.format(plant_name=plant_name, unit_id=unit_id)
            for template in config.extraction.search_query_templates["unit_technical"]
        ]
        
        unit_results = await self.search_client.multi_query_search(unit_queries)
        return self._flatten_results(unit_results)
    
    def _flatten_results(self, results_dict: Dict[str, List[SearchResult]]) -> List[SearchResult]:
        """Flatten and deduplicate search results."""
        all_results = []
        seen_urls = set()
        
        for query_results in results_dict.values():
            for result in query_results:
                if result.url not in seen_urls:
                    all_results.append(result)
                    seen_urls.add(result.url)
        
        # Sort by source type priority and rank
        priority_weights = config.url_priority_weights
        all_results.sort(
            key=lambda r: (
                -priority_weights.get(r.source_type.value, 0),
                r.rank
            )
        )
        
        return all_results
