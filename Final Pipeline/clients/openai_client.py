"""
OpenAI client with vision capabilities for data extraction.
Supports both text-based and vision-enhanced extraction using GPT-4o-mini.
"""

import asyncio
import logging
import json
import base64
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from openai import AsyncOpenAI
from tenacity import retry, stop_after_attempt, wait_exponential

from ..core import (
    config, ExtractionResult, DocumentType, 
    OpenAIAPIError, VisionProcessingError, handle_api_error
)

logger = logging.getLogger(__name__)


class OpenAIExtractionClient:
    """
    OpenAI client with vision capabilities for universal data extraction.
    Supports both text-based and vision-enhanced extraction.
    """
    
    def __init__(self, api_key: Optional[str] = None, model: Optional[str] = None):
        """Initialize the OpenAI extraction client."""
        self.api_key = api_key or config.api.openai_api_key
        self.model = model or config.api.openai_model
        
        if not self.api_key:
            raise OpenAIAPIError("OpenAI API key is required")
        
        self.client = AsyncOpenAI(api_key=self.api_key)
        self.extraction_count = 0
        self.vision_extraction_count = 0
        self.total_tokens_used = 0
        
        logger.info(f"OpenAI extraction client initialized with model: {self.model}")
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    @handle_api_error
    async def extract_field(
        self,
        field_name: str,
        content: str,
        context: str,
        document_type: DocumentType = DocumentType.HTML,
        image_data: Optional[bytes] = None
    ) -> ExtractionResult:
        """
        Extract a specific field from content using OpenAI.
        
        Args:
            field_name: Name of the field to extract
            content: Text content to extract from
            context: Context/description for the field
            document_type: Type of document being processed
            image_data: Optional image data for vision processing
            
        Returns:
            ExtractionResult with extracted value and confidence
        """
        try:
            self.extraction_count += 1
            
            # Choose extraction method based on document type and available data
            if document_type == DocumentType.PDF_SCANNED and image_data:
                return await self._extract_with_vision(field_name, content, context, image_data)
            else:
                return await self._extract_with_text(field_name, content, context)
                
        except Exception as e:
            logger.error(f"Field extraction failed for {field_name}: {str(e)}")
            raise OpenAIAPIError(f"Failed to extract field {field_name}: {str(e)}", model=self.model)
    
    async def _extract_with_text(self, field_name: str, content: str, context: str) -> ExtractionResult:
        """Extract field using text-based processing."""
        prompt = self._create_text_extraction_prompt(field_name, content, context)
        
        try:
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert power plant data extraction specialist. Extract precise information from documents with high accuracy. Always respond with valid JSON containing the extracted value and confidence score."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=config.extraction.field_extraction_temperature,
                max_tokens=config.extraction.field_extraction_max_tokens,
                response_format={"type": "json_object"}
            )
            
            # Update token usage
            if response.usage:
                self.total_tokens_used += response.usage.total_tokens
            
            # Parse response
            result_text = response.choices[0].message.content
            result_data = json.loads(result_text)
            
            return ExtractionResult(
                field_name=field_name,
                extracted_value=result_data.get("value"),
                confidence_score=float(result_data.get("confidence", 0.0)),
                source_url="text_content",
                extraction_method="text_based",
                raw_content=content[:500]  # Store first 500 chars for reference
            )
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON response for {field_name}: {str(e)}")
            return ExtractionResult(
                field_name=field_name,
                extracted_value=None,
                confidence_score=0.0,
                source_url="text_content",
                extraction_method="text_based_failed",
                raw_content=content[:500]
            )
        except Exception as e:
            raise OpenAIAPIError(f"Text extraction failed: {str(e)}", model=self.model)
    
    async def _extract_with_vision(self, field_name: str, content: str, context: str, image_data: bytes) -> ExtractionResult:
        """Extract field using vision-enhanced processing."""
        self.vision_extraction_count += 1
        
        try:
            # Encode image to base64
            image_base64 = base64.b64encode(image_data).decode('utf-8')
            
            prompt = self._create_vision_extraction_prompt(field_name, context)
            
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert at extracting power plant data from scanned documents and images. Analyze the image carefully and extract the requested information with high accuracy. Always respond with valid JSON."
                    },
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": prompt
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{image_base64}",
                                    "detail": "high"
                                }
                            }
                        ]
                    }
                ],
                temperature=config.extraction.field_extraction_temperature,
                max_tokens=config.extraction.field_extraction_max_tokens,
                response_format={"type": "json_object"}
            )
            
            # Update token usage
            if response.usage:
                self.total_tokens_used += response.usage.total_tokens
            
            # Parse response
            result_text = response.choices[0].message.content
            result_data = json.loads(result_text)
            
            return ExtractionResult(
                field_name=field_name,
                extracted_value=result_data.get("value"),
                confidence_score=float(result_data.get("confidence", 0.0)),
                source_url="vision_processed_image",
                extraction_method="vision_enhanced",
                raw_content=f"Vision processed image ({len(image_data)} bytes)"
            )
            
        except Exception as e:
            logger.error(f"Vision extraction failed for {field_name}: {str(e)}")
            raise VisionProcessingError(f"Vision extraction failed: {str(e)}", document_type="scanned_pdf")
    
    def _create_text_extraction_prompt(self, field_name: str, content: str, context: str) -> str:
        """Create extraction prompt for text-based processing."""
        return f"""
Extract the following field from the provided content:

Field Name: {field_name}
Field Description: {context}

Content to analyze:
{content[:config.pipeline.max_content_length]}

Instructions:
1. Look for information related to "{field_name}" based on the description: "{context}"
2. Extract the most accurate and specific value you can find
3. If the information is not clearly present, return null for the value
4. Provide a confidence score between 0.0 and 1.0 based on how certain you are

Respond with valid JSON in this format:
{{
    "value": "extracted_value_or_null",
    "confidence": 0.85,
    "reasoning": "Brief explanation of why you extracted this value"
}}
"""
    
    def _create_vision_extraction_prompt(self, field_name: str, context: str) -> str:
        """Create extraction prompt for vision-enhanced processing."""
        return f"""
Analyze the provided image/document and extract the following information:

Field Name: {field_name}
Field Description: {context}

Instructions:
1. Carefully examine the image for information related to "{field_name}"
2. Look for text, tables, charts, or any visual elements that contain this information
3. Extract the most accurate value you can identify
4. If the information is not visible or unclear, return null for the value
5. Provide a confidence score based on the clarity and certainty of the information

Respond with valid JSON in this format:
{{
    "value": "extracted_value_or_null",
    "confidence": 0.85,
    "reasoning": "Brief explanation of what you found in the image"
}}
"""
    
    async def extract_multiple_fields(
        self,
        fields: Dict[str, str],
        content: str,
        document_type: DocumentType = DocumentType.HTML,
        image_data: Optional[bytes] = None
    ) -> Dict[str, ExtractionResult]:
        """
        Extract multiple fields from content in a single operation.
        
        Args:
            fields: Dictionary of field_name -> field_description
            content: Content to extract from
            document_type: Type of document
            image_data: Optional image data for vision processing
            
        Returns:
            Dictionary of field_name -> ExtractionResult
        """
        results = {}
        
        # Process fields in batches to avoid overwhelming the API
        batch_size = 5
        field_items = list(fields.items())
        
        for i in range(0, len(field_items), batch_size):
            batch = field_items[i:i + batch_size]
            
            # Process batch concurrently
            tasks = [
                self.extract_field(field_name, content, field_desc, document_type, image_data)
                for field_name, field_desc in batch
            ]
            
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Store results
            for (field_name, _), result in zip(batch, batch_results):
                if isinstance(result, Exception):
                    logger.error(f"Failed to extract {field_name}: {str(result)}")
                    results[field_name] = ExtractionResult(
                        field_name=field_name,
                        extracted_value=None,
                        confidence_score=0.0,
                        source_url="error",
                        extraction_method="failed",
                        raw_content=str(result)
                    )
                else:
                    results[field_name] = result
            
            # Rate limiting between batches
            if i + batch_size < len(field_items):
                await asyncio.sleep(config.pipeline.extraction_delay)
        
        return results
    
    def get_usage_stats(self) -> Dict[str, Any]:
        """Get usage statistics for the client."""
        return {
            "total_extractions": self.extraction_count,
            "vision_extractions": self.vision_extraction_count,
            "text_extractions": self.extraction_count - self.vision_extraction_count,
            "total_tokens_used": self.total_tokens_used,
            "model": self.model
        }
