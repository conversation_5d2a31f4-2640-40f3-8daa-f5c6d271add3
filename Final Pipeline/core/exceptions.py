"""
Custom exceptions for the Universal Hierarchical Power Plant Extraction Pipeline.
Provides specific error handling for different pipeline components.
"""

from typing import Optional, Dict, Any


class PipelineException(Exception):
    """Base exception for all pipeline-related errors."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.message = message
        self.details = details or {}
    
    def __str__(self):
        if self.details:
            return f"{self.message} | Details: {self.details}"
        return self.message


class ConfigurationError(PipelineException):
    """Raised when there are configuration-related issues."""
    pass


class APIError(PipelineException):
    """Base class for API-related errors."""
    
    def __init__(self, message: str, api_name: str, status_code: Optional[int] = None, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, details)
        self.api_name = api_name
        self.status_code = status_code


class ScraperAPIError(APIError):
    """Raised when Scraper API encounters errors."""
    
    def __init__(self, message: str, status_code: Optional[int] = None, url: Optional[str] = None):
        details = {"url": url} if url else None
        super().__init__(message, "Scraper API", status_code, details)


class OpenAIAPIError(APIError):
    """Raised when OpenAI API encounters errors."""
    
    def __init__(self, message: str, model: Optional[str] = None, tokens_used: Optional[int] = None):
        details = {"model": model, "tokens_used": tokens_used}
        super().__init__(message, "OpenAI API", details=details)


class SearchError(PipelineException):
    """Raised when search operations fail."""
    
    def __init__(self, message: str, query: Optional[str] = None, search_type: Optional[str] = None):
        details = {"query": query, "search_type": search_type}
        super().__init__(message, details)


class ExtractionError(PipelineException):
    """Raised when data extraction fails."""
    
    def __init__(self, message: str, field_name: Optional[str] = None, extraction_level: Optional[str] = None, plant_name: Optional[str] = None):
        details = {
            "field_name": field_name,
            "extraction_level": extraction_level,
            "plant_name": plant_name
        }
        super().__init__(message, details)


class SchemaError(PipelineException):
    """Raised when schema-related operations fail."""
    
    def __init__(self, message: str, schema_file: Optional[str] = None, field_name: Optional[str] = None):
        details = {"schema_file": schema_file, "field_name": field_name}
        super().__init__(message, details)


class ValidationError(PipelineException):
    """Raised when data validation fails."""
    
    def __init__(self, message: str, field_name: Optional[str] = None, expected_type: Optional[str] = None, actual_value: Optional[Any] = None):
        details = {
            "field_name": field_name,
            "expected_type": expected_type,
            "actual_value": str(actual_value) if actual_value is not None else None
        }
        super().__init__(message, details)


class VisionProcessingError(PipelineException):
    """Raised when vision processing fails."""
    
    def __init__(self, message: str, document_type: Optional[str] = None, file_size: Optional[int] = None):
        details = {"document_type": document_type, "file_size": file_size}
        super().__init__(message, details)


class CacheError(PipelineException):
    """Raised when cache operations fail."""
    
    def __init__(self, message: str, cache_key: Optional[str] = None, operation: Optional[str] = None):
        details = {"cache_key": cache_key, "operation": operation}
        super().__init__(message, details)


class RateLimitError(APIError):
    """Raised when API rate limits are exceeded."""
    
    def __init__(self, message: str, api_name: str, retry_after: Optional[int] = None):
        details = {"retry_after": retry_after}
        super().__init__(message, api_name, details=details)


class ContentProcessingError(PipelineException):
    """Raised when content processing fails."""
    
    def __init__(self, message: str, url: Optional[str] = None, content_type: Optional[str] = None, content_length: Optional[int] = None):
        details = {
            "url": url,
            "content_type": content_type,
            "content_length": content_length
        }
        super().__init__(message, details)


class PipelineTimeoutError(PipelineException):
    """Raised when pipeline operations timeout."""
    
    def __init__(self, message: str, operation: Optional[str] = None, timeout_seconds: Optional[int] = None):
        details = {"operation": operation, "timeout_seconds": timeout_seconds}
        super().__init__(message, details)


# Utility functions for error handling

def handle_api_error(func):
    """Decorator to handle common API errors."""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            if "rate limit" in str(e).lower():
                raise RateLimitError(f"Rate limit exceeded: {str(e)}", "Unknown API")
            elif "timeout" in str(e).lower():
                raise PipelineTimeoutError(f"Operation timed out: {str(e)}")
            else:
                raise PipelineException(f"Unexpected error in {func.__name__}: {str(e)}")
    return wrapper


def create_extraction_error(field_name: str, plant_name: str, level: str, original_error: Exception) -> ExtractionError:
    """Create a standardized extraction error."""
    return ExtractionError(
        f"Failed to extract field '{field_name}' for {plant_name} at {level} level: {str(original_error)}",
        field_name=field_name,
        extraction_level=level,
        plant_name=plant_name
    )


def create_api_error(api_name: str, operation: str, original_error: Exception) -> APIError:
    """Create a standardized API error."""
    return APIError(
        f"{api_name} error during {operation}: {str(original_error)}",
        api_name=api_name
    )
