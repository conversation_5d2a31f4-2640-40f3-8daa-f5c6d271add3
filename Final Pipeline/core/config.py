"""
Core configuration for the Universal Hierarchical Power Plant Extraction Pipeline.
Handles environment variables, API keys, and pipeline settings.
"""

import os
from typing import Dict, List, Optional
from dataclasses import dataclass
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


@dataclass
class APIConfig:
    """API configuration settings."""
    scraper_api_key: str
    openai_api_key: str
    openai_model: str = "gpt-4o-mini"  # Vision-capable model
    
    def __post_init__(self):
        """Validate API keys are present."""
        if not self.scraper_api_key:
            raise ValueError("SCRAPER_API_KEY environment variable is required")
        if not self.openai_api_key:
            raise ValueError("OPENAI_API_KEY environment variable is required")


@dataclass
class PipelineConfig:
    """Pipeline processing configuration."""
    max_search_results: int = 10
    max_scrape_pages: int = 5
    request_timeout: int = 60
    retry_attempts: int = 3
    min_content_length: int = 100
    max_content_length: int = 50000
    confidence_threshold: float = 0.7
    
    # Vision processing settings
    max_image_size: tuple = (1024, 1024)
    max_pages_per_extraction: int = 5
    
    # Rate limiting
    search_delay: float = 2.0
    extraction_delay: float = 1.0


@dataclass
class ExtractionConfig:
    """Extraction-specific configuration."""
    # Schema file paths
    org_schema_path: str = "Final Pipeline/org_level.json"
    plant_schema_path: str = "Final Pipeline/plant_level.json"
    unit_schema_path: str = "Final Pipeline/unit_level.json"
    
    # Extraction strategies
    use_vision_processing: bool = True
    enable_source_tracking: bool = True
    enable_caching: bool = True
    
    # Field extraction settings
    field_extraction_temperature: float = 0.1
    field_extraction_max_tokens: int = 500
    
    # Search query templates
    search_query_templates: Dict[str, List[str]] = None
    
    def __post_init__(self):
        """Initialize search query templates."""
        if self.search_query_templates is None:
            self.search_query_templates = {
                "organizational": [
                    "{plant_name} power plant company owner organization",
                    "{plant_name} power station ownership structure",
                    "{plant_name} utility company operator",
                    "{plant_name} private public government ownership",
                    "{plant_name} corporate information annual report"
                ],
                "plant_technical": [
                    "{plant_name} technical specifications capacity MW",
                    "{plant_name} power plant location coordinates address",
                    "{plant_name} grid connection transmission substation",
                    "{plant_name} power purchase agreement PPA contract",
                    "{plant_name} operational units generators list"
                ],
                "unit_technical": [
                    "{plant_name} unit {unit_id} specifications capacity",
                    "{plant_name} unit {unit_id} efficiency performance",
                    "{plant_name} unit {unit_id} fuel consumption emissions",
                    "{plant_name} unit {unit_id} operational data PLF PAF",
                    "{plant_name} unit {unit_id} technical parameters"
                ]
            }


class Config:
    """Main configuration class that combines all settings."""
    
    def __init__(self):
        """Initialize configuration from environment variables."""
        self.api = APIConfig(
            scraper_api_key=os.getenv('SCRAPER_API_KEY', ''),
            openai_api_key=os.getenv('OPENAI_API_KEY', ''),
            openai_model=os.getenv('OPENAI_MODEL', 'gpt-4o-mini')
        )
        
        self.pipeline = PipelineConfig(
            max_search_results=int(os.getenv('MAX_SEARCH_RESULTS', 10)),
            max_scrape_pages=int(os.getenv('MAX_SCRAPE_PAGES', 5)),
            request_timeout=int(os.getenv('REQUEST_TIMEOUT', 60)),
            retry_attempts=int(os.getenv('RETRY_ATTEMPTS', 3)),
            min_content_length=int(os.getenv('MIN_CONTENT_LENGTH', 100)),
            max_content_length=int(os.getenv('MAX_CONTENT_LENGTH', 50000)),
            confidence_threshold=float(os.getenv('CONFIDENCE_THRESHOLD', 0.7))
        )
        
        self.extraction = ExtractionConfig()
    
    @property
    def source_type_patterns(self) -> Dict[str, List[str]]:
        """URL patterns for different source types."""
        return {
            "company_official": [
                "company.com", "corp.com", "power.com", "energy.com",
                "official", "corporate", "investor"
            ],
            "regulatory_filing": [
                "sec.gov", "sebi.gov.in", "regulatory", "filing",
                "annual-report", "10-k", "10-q"
            ],
            "government_database": [
                "gov.in", "gov.uk", "gov.au", ".gov", "ministry",
                "department", "authority", "commission"
            ],
            "industry_report": [
                "iea.org", "irena.org", "platts.com", "eia.gov",
                "industry", "report", "analysis", "market"
            ],
            "news_article": [
                "reuters.com", "bloomberg.com", "news", "times",
                "economic", "business", "financial"
            ],
            "wikipedia": [
                "wikipedia.org", "wikimedia"
            ]
        }
    
    @property
    def url_priority_weights(self) -> Dict[str, int]:
        """Priority weights for different source types."""
        return {
            "company_official": 10,
            "regulatory_filing": 9,
            "government_database": 8,
            "industry_report": 7,
            "news_article": 6,
            "wikipedia": 5,
            "other": 3
        }


# Global configuration instance
config = Config()
