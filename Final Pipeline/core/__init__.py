"""
Core module for the Universal Hierarchical Power Plant Extraction Pipeline.
Provides configuration, models, and exception handling.
"""

from .config import Config, APIConfig, PipelineConfig, ExtractionConfig, config
from .models import (
    ExtractionLevel,
    DocumentType,
    SourceType,
    SearchResult,
    ScrapedContent,
    ExtractionResult,
    FieldDefinition,
    SchemaDefinition,
    ExtractionSession,
    PipelineResult
)
from .exceptions import (
    PipelineException,
    ConfigurationError,
    APIError,
    ScraperAPIError,
    OpenAIAPIError,
    SearchError,
    ExtractionError,
    SchemaError,
    ValidationError,
    VisionProcessingError,
    CacheError,
    RateLimitError,
    ContentProcessingError,
    PipelineTimeoutError,
    handle_api_error,
    create_extraction_error,
    create_api_error
)

__all__ = [
    # Configuration
    'Config',
    'APIConfig', 
    'PipelineConfig',
    'ExtractionConfig',
    'config',
    
    # Models
    'ExtractionLevel',
    'DocumentType',
    'SourceType',
    'SearchResult',
    'ScrapedContent',
    'ExtractionResult',
    'FieldDefinition',
    'SchemaDefinition',
    'ExtractionSession',
    'PipelineResult',
    
    # Exceptions
    'PipelineException',
    'ConfigurationError',
    'APIError',
    'ScraperAPIError',
    'OpenAIAPIError',
    'SearchError',
    'ExtractionError',
    'SchemaError',
    'ValidationError',
    'VisionProcessingError',
    'CacheError',
    'RateLimitError',
    'ContentProcessingError',
    'PipelineTimeoutError',
    'handle_api_error',
    'create_extraction_error',
    'create_api_error'
]
