"""
Core data models for the Universal Hierarchical Power Plant Extraction Pipeline.
Defines all data structures used throughout the pipeline.
"""

from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum


class ExtractionLevel(Enum):
    """Extraction levels in the hierarchical pipeline."""
    ORGANIZATIONAL = "organizational"
    PLANT = "plant"
    UNIT = "unit"


class DocumentType(Enum):
    """Types of documents that can be processed."""
    HTML = "html"
    PDF_TEXT = "pdf_text"
    PDF_SCANNED = "pdf_scanned"
    IMAGE = "image"


class SourceType(Enum):
    """Types of data sources."""
    COMPANY_OFFICIAL = "company_official"
    REGULATORY_FILING = "regulatory_filing"
    GOVERNMENT_DATABASE = "government_database"
    INDUSTRY_REPORT = "industry_report"
    NEWS_ARTICLE = "news_article"
    WIKIPEDIA = "wikipedia"
    OTHER = "other"


@dataclass
class SearchResult:
    """Represents a search result from web search."""
    title: str
    url: str
    snippet: str
    rank: int
    source_type: SourceType = SourceType.OTHER
    
    def __post_init__(self):
        """Clean and validate search result data."""
        self.title = self.title.strip() if self.title else ""
        self.url = self.url.strip() if self.url else ""
        self.snippet = self.snippet.strip() if self.snippet else ""


@dataclass
class ScrapedContent:
    """Represents scraped content from a web page."""
    url: str
    content: str
    title: str
    document_type: DocumentType
    source_type: SourceType = SourceType.OTHER
    scraped_at: datetime = field(default_factory=datetime.now)
    content_length: int = 0
    is_pdf: bool = False
    pdf_pages: int = 0
    
    def __post_init__(self):
        """Calculate content length and validate data."""
        self.content_length = len(self.content) if self.content else 0
        self.is_pdf = self.document_type in [DocumentType.PDF_TEXT, DocumentType.PDF_SCANNED]


@dataclass
class ExtractionResult:
    """Represents the result of a field extraction."""
    field_name: str
    extracted_value: Any
    confidence_score: float
    source_url: str
    extraction_method: str
    raw_content: str = ""
    extraction_timestamp: datetime = field(default_factory=datetime.now)
    
    def is_valid(self, threshold: float = 0.7) -> bool:
        """Check if extraction result meets confidence threshold."""
        return self.confidence_score >= threshold and self.extracted_value is not None


@dataclass
class FieldDefinition:
    """Represents a field definition from schema."""
    name: str
    description: str
    data_type: str
    is_required: bool = True
    is_nested: bool = False
    nested_fields: List['FieldDefinition'] = field(default_factory=list)
    
    def generate_search_context(self) -> str:
        """Generate search context for this field."""
        return f"{self.name}: {self.description}"
    
    def generate_extraction_prompt(self, plant_name: str, unit_id: str = None) -> str:
        """Generate extraction prompt for this field."""
        context = f"Extract the '{self.name}' field for {plant_name}"
        if unit_id:
            context += f" Unit {unit_id}"
        context += f". Field description: {self.description}"
        return context


@dataclass
class SchemaDefinition:
    """Represents a complete schema definition."""
    level: ExtractionLevel
    fields: List[FieldDefinition]
    total_fields: int = 0
    
    def __post_init__(self):
        """Calculate total field count including nested fields."""
        self.total_fields = len(self.fields)
        for field_def in self.fields:
            if field_def.is_nested:
                self.total_fields += len(field_def.nested_fields)


@dataclass
class ExtractionSession:
    """Represents an extraction session for tracking progress."""
    plant_name: str
    session_id: str
    start_time: datetime = field(default_factory=datetime.now)
    end_time: Optional[datetime] = None
    current_level: Optional[ExtractionLevel] = None
    
    # Progress tracking
    org_completed: bool = False
    plant_completed: bool = False
    unit_completed: bool = False
    
    # Results storage
    org_data: Dict[str, Any] = field(default_factory=dict)
    plant_data: Dict[str, Any] = field(default_factory=dict)
    unit_data: List[Dict[str, Any]] = field(default_factory=list)
    
    # Source tracking
    sources_used: List[str] = field(default_factory=list)
    extraction_stats: Dict[str, Any] = field(default_factory=dict)
    
    def mark_level_complete(self, level: ExtractionLevel):
        """Mark a level as completed."""
        if level == ExtractionLevel.ORGANIZATIONAL:
            self.org_completed = True
        elif level == ExtractionLevel.PLANT:
            self.plant_completed = True
        elif level == ExtractionLevel.UNIT:
            self.unit_completed = True
    
    def is_complete(self) -> bool:
        """Check if all levels are completed."""
        return self.org_completed and self.plant_completed and self.unit_completed
    
    def get_completion_percentage(self) -> float:
        """Get overall completion percentage."""
        completed = sum([self.org_completed, self.plant_completed, self.unit_completed])
        return (completed / 3) * 100


@dataclass
class PipelineResult:
    """Final result of the complete pipeline execution."""
    plant_name: str
    session_id: str
    execution_time: float
    success: bool
    
    # Extracted data
    organizational_data: Dict[str, Any]
    plant_data: Dict[str, Any]
    unit_data: List[Dict[str, Any]]
    
    # Metadata
    total_fields_extracted: int
    total_sources_used: int
    extraction_stats: Dict[str, Any]
    error_message: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            "plant_name": self.plant_name,
            "session_id": self.session_id,
            "execution_time": self.execution_time,
            "success": self.success,
            "organizational_data": self.organizational_data,
            "plant_data": self.plant_data,
            "unit_data": self.unit_data,
            "metadata": {
                "total_fields_extracted": self.total_fields_extracted,
                "total_sources_used": self.total_sources_used,
                "extraction_stats": self.extraction_stats,
                "error_message": self.error_message
            }
        }
