"""
Main entry point for the Universal Hierarchical Power Plant Extraction Pipeline.
Provides a simple interface for extracting complete power plant data.
"""

import asyncio
import json
import logging
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Optional

from core import config, PipelineResult
from orchestrators import PipelineOrchestrator

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('pipeline.log')
    ]
)
logger = logging.getLogger(__name__)


class UniversalPowerPlantPipeline:
    """Main pipeline class for universal power plant data extraction."""
    
    def __init__(self):
        """Initialize the universal pipeline."""
        self.orchestrator = PipelineOrchestrator()
        self.output_dir = Path("output")
        self.output_dir.mkdir(exist_ok=True)
        
        logger.info("Universal Power Plant Pipeline initialized")
        logger.info(f"Output directory: {self.output_dir}")
    
    async def extract_plant_data(self, plant_name: str) -> PipelineResult:
        """
        Extract complete hierarchical data for a power plant.
        
        Args:
            plant_name: Name of the power plant (e.g., "Jhajjar Power Plant")
            
        Returns:
            PipelineResult with all extracted data
        """
        logger.info(f"🚀 Starting extraction for: {plant_name}")
        
        try:
            # Validate input
            if not plant_name or not plant_name.strip():
                raise ValueError("Plant name cannot be empty")
            
            plant_name = plant_name.strip()
            
            # Run the complete extraction
            result = await self.orchestrator.extract_complete_hierarchy(plant_name)
            
            # Save results
            await self._save_results(result)
            
            # Print summary
            self._print_extraction_summary(result)
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Extraction failed for {plant_name}: {str(e)}")
            raise
    
    async def _save_results(self, result: PipelineResult):
        """Save extraction results to files."""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            plant_name_safe = result.plant_name.replace(" ", "_").replace("-", "_")
            
            # Save complete result
            complete_file = self.output_dir / f"{plant_name_safe}_complete_{timestamp}.json"
            with open(complete_file, 'w', encoding='utf-8') as f:
                json.dump(result.to_dict(), f, indent=2, ensure_ascii=False)
            
            # Save individual level files
            org_file = self.output_dir / f"{plant_name_safe}_organizational_{timestamp}.json"
            with open(org_file, 'w', encoding='utf-8') as f:
                json.dump(result.organizational_data, f, indent=2, ensure_ascii=False)
            
            plant_file = self.output_dir / f"{plant_name_safe}_plant_{timestamp}.json"
            with open(plant_file, 'w', encoding='utf-8') as f:
                json.dump(result.plant_data, f, indent=2, ensure_ascii=False)
            
            units_file = self.output_dir / f"{plant_name_safe}_units_{timestamp}.json"
            with open(units_file, 'w', encoding='utf-8') as f:
                json.dump(result.unit_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"📁 Results saved:")
            logger.info(f"   Complete: {complete_file}")
            logger.info(f"   Organizational: {org_file}")
            logger.info(f"   Plant: {plant_file}")
            logger.info(f"   Units: {units_file}")
            
        except Exception as e:
            logger.error(f"Failed to save results: {str(e)}")
    
    def _print_extraction_summary(self, result: PipelineResult):
        """Print a summary of the extraction results."""
        print("\n" + "="*80)
        print(f"🏭 EXTRACTION SUMMARY: {result.plant_name}")
        print("="*80)
        
        print(f"✅ Success: {result.success}")
        print(f"⏱️  Execution Time: {result.execution_time:.2f} seconds")
        print(f"📊 Total Fields Extracted: {result.total_fields_extracted}")
        print(f"🌐 Total Sources Used: {result.total_sources_used}")
        
        if result.error_message:
            print(f"❌ Error: {result.error_message}")
        
        print("\n📋 ORGANIZATIONAL DATA:")
        org_data = result.organizational_data
        print(f"   Organization: {org_data.get('organization_name', 'Not found')}")
        print(f"   Country: {org_data.get('country_name', 'Not found')}")
        print(f"   Type: {org_data.get('cfpp_type', 'Not found')}")
        print(f"   Plant Types: {org_data.get('plant_types', 'Not found')}")
        
        print("\n🏭 PLANT DATA:")
        plant_data = result.plant_data
        print(f"   Name: {plant_data.get('name', 'Not found')}")
        print(f"   Location: {plant_data.get('lat', 'N/A')}, {plant_data.get('long', 'N/A')}")
        print(f"   Address: {plant_data.get('plant_address', 'Not found')}")
        print(f"   Type: {plant_data.get('plant_type', 'Not found')}")
        print(f"   Units: {plant_data.get('units_id', [])}")
        
        print("\n⚡ UNIT DATA:")
        for i, unit_data in enumerate(result.unit_data):
            unit_num = unit_data.get('unit_number', i+1)
            capacity = unit_data.get('capacity', 'N/A')
            technology = unit_data.get('technology', 'N/A')
            print(f"   Unit {unit_num}: {capacity} MW, {technology}")
        
        # Extraction statistics
        stats = result.extraction_stats
        if stats:
            print(f"\n📈 EXTRACTION STATISTICS:")
            completion = stats.get('completion_percentage', 0)
            print(f"   Completion: {completion:.1f}%")
            
            levels = stats.get('levels_completed', {})
            print(f"   Levels: Org({levels.get('organizational', False)}), "
                  f"Plant({levels.get('plant', False)}), "
                  f"Unit({levels.get('unit', False)})")
            
            openai_usage = stats.get('openai_usage', {})
            if openai_usage:
                print(f"   OpenAI: {openai_usage.get('total_extractions', 0)} calls, "
                      f"{openai_usage.get('total_tokens_used', 0)} tokens")
        
        print("="*80)
    
    def get_schema_summary(self) -> dict:
        """Get a summary of all schemas."""
        return self.orchestrator.schema_processor.get_schema_summary()
    
    def clear_cache(self) -> int:
        """Clear all cached data."""
        return self.orchestrator.cache_manager.clear_all_cache()
    
    def get_cache_stats(self) -> dict:
        """Get cache statistics."""
        return self.orchestrator.cache_manager.get_cache_stats()


async def main():
    """Main function for command-line usage."""
    if len(sys.argv) < 2:
        print("Usage: python main.py '<plant_name>'")
        print("Example: python main.py 'Jhajjar Power Plant'")
        sys.exit(1)
    
    plant_name = sys.argv[1]
    
    try:
        # Initialize pipeline
        pipeline = UniversalPowerPlantPipeline()
        
        # Show schema summary
        print("📋 Schema Summary:")
        schema_summary = pipeline.get_schema_summary()
        for level, info in schema_summary.items():
            print(f"   {level.title()}: {info['total_fields']} fields")
        
        print(f"\n🔧 Configuration:")
        print(f"   OpenAI Model: {config.api.openai_model}")
        print(f"   Vision Processing: {config.extraction.use_vision_processing}")
        print(f"   Caching: {config.extraction.enable_caching}")
        print(f"   Source Tracking: {config.extraction.enable_source_tracking}")
        
        # Run extraction
        result = await pipeline.extract_plant_data(plant_name)
        
        if result.success:
            print(f"\n✅ Extraction completed successfully!")
        else:
            print(f"\n❌ Extraction failed: {result.error_message}")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⏹️  Extraction cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Fatal error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
