"""
Unit level extractor for detailed technical specifications of individual power plant units.
Extracts all fields from the unit_level.json schema including complex technical parameters.
"""

import logging
import json
from typing import Dict, List, Any, Optional

from ..core import ExtractionLevel, ScrapedContent
from .base_extractor import BaseExtractor

logger = logging.getLogger(__name__)


class UnitExtractor(BaseExtractor):
    """Extractor for unit-level power plant data."""
    
    def _get_extraction_level(self) -> ExtractionLevel:
        """Get the extraction level for this extractor."""
        return ExtractionLevel.UNIT
    
    def _get_search_queries(self, plant_name: str, unit_id: Optional[str] = None) -> List[str]:
        """Get search queries for unit information."""
        base_queries = [
            f"{plant_name} unit specifications technical details",
            f"{plant_name} generator capacity efficiency performance",
            f"{plant_name} unit fuel consumption emissions data",
            f"{plant_name} operational performance PLF PAF statistics",
            f"{plant_name} technical parameters heat rate efficiency",
            f"{plant_name} unit commissioning operational dates",
            f"{plant_name} power generation statistics annual data"
        ]
        
        if unit_id:
            unit_specific_queries = [
                f"{plant_name} unit {unit_id} specifications capacity",
                f"{plant_name} unit {unit_id} efficiency performance data",
                f"{plant_name} unit {unit_id} fuel consumption technical",
                f"{plant_name} unit {unit_id} operational statistics"
            ]
            return unit_specific_queries + base_queries
        
        return base_queries
    
    async def extract_unit_data(
        self,
        plant_name: str,
        unit_ids: List[str],
        org_context: Optional[Dict[str, Any]] = None,
        plant_context: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Extract complete unit data for all units of a power plant.
        
        Args:
            plant_name: Name of the power plant
            unit_ids: List of unit identifiers
            org_context: Optional organizational context data
            plant_context: Optional plant context data
            
        Returns:
            List of dictionaries with unit data matching unit_level.json schema
        """
        logger.info(f"⚡ Starting unit extraction for: {plant_name} ({len(unit_ids)} units)")
        
        all_units_data = []
        
        for unit_id in unit_ids:
            try:
                logger.info(f"Extracting data for Unit {unit_id}")
                
                unit_data = await self._extract_single_unit_data(
                    plant_name, unit_id, org_context, plant_context
                )
                
                all_units_data.append(unit_data)
                
            except Exception as e:
                logger.error(f"Failed to extract Unit {unit_id} data: {str(e)}")
                # Add empty unit data to maintain structure
                all_units_data.append(self._create_empty_unit_data(unit_id))
        
        logger.info(f"Unit extraction completed for {len(all_units_data)} units")
        return all_units_data
    
    async def _extract_single_unit_data(
        self,
        plant_name: str,
        unit_id: str,
        org_context: Optional[Dict[str, Any]] = None,
        plant_context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Extract data for a single unit."""
        
        # Get unit-specific search queries
        search_queries = self._get_search_queries(plant_name, unit_id)
        
        # Search and scrape content
        scraped_contents = await self.search_and_scrape_for_fields(
            plant_name, search_queries, unit_id
        )
        
        if not scraped_contents:
            logger.warning(f"No content found for Unit {unit_id} extraction")
            return self._create_empty_unit_data(unit_id)
        
        logger.info(f"Found {len(scraped_contents)} content sources for Unit {unit_id}")
        
        # Extract simple fields
        unit_data = await self.extract_simple_fields(plant_name, scraped_contents, unit_id)
        
        # Extract complex array fields
        unit_data = await self._extract_array_fields(
            unit_data, plant_name, unit_id, scraped_contents
        )
        
        # Post-process and enhance data
        unit_data = await self._post_process_unit_data(
            unit_data, plant_name, unit_id, scraped_contents, org_context, plant_context
        )
        
        # Validate extracted data
        validation_results = self.data_validator.validate_complete_data(
            ExtractionLevel.UNIT, unit_data
        )
        
        logger.info(f"Unit {unit_id} extraction completed. Validation score: {validation_results['validation_score']:.1f}%")
        
        # Add metadata
        unit_data["_extraction_metadata"] = {
            "extraction_level": "unit",
            "plant_name": plant_name,
            "unit_id": unit_id,
            "sources_count": len(scraped_contents),
            "validation_score": validation_results["validation_score"],
            "extraction_stats": self.get_extraction_stats()
        }
        
        return unit_data
    
    async def _extract_array_fields(
        self,
        unit_data: Dict[str, Any],
        plant_name: str,
        unit_id: str,
        scraped_contents: List[ScrapedContent]
    ) -> Dict[str, Any]:
        """Extract array fields that contain time-series or multi-value data."""
        
        array_fields = [
            "auxiliary_power_consumed",
            "emission_factor", 
            "fuel_type",
            "gross_power_generation",
            "PAF",
            "plf",
            "ppa_details"
        ]
        
        for field_name in array_fields:
            unit_data[field_name] = await self._extract_array_field(
                field_name, plant_name, unit_id, scraped_contents
            )
        
        return unit_data
    
    async def _extract_array_field(
        self,
        field_name: str,
        plant_name: str,
        unit_id: str,
        scraped_contents: List[ScrapedContent]
    ) -> List[Dict[str, Any]]:
        """Extract a specific array field."""
        try:
            # Get field definition for context
            field_def = self.schema_processor.get_field_definition(
                ExtractionLevel.UNIT, field_name
            )
            
            if not field_def:
                return []
            
            context = self._get_array_field_context(field_name, field_def.description)
            
            for content in scraped_contents:
                result = await self.openai_client.extract_field(
                    field_name=field_name,
                    content=content.content,
                    context=f"Extract {field_name} data for {plant_name} Unit {unit_id}. {context}",
                    document_type=content.document_type
                )
                
                if result.extracted_value and result.confidence_score > 0.3:
                    try:
                        if isinstance(result.extracted_value, str):
                            array_data = json.loads(result.extracted_value)
                        else:
                            array_data = result.extracted_value
                        
                        if isinstance(array_data, list):
                            return array_data
                    except (json.JSONDecodeError, TypeError):
                        continue
            
            return []
            
        except Exception as e:
            logger.error(f"Failed to extract array field {field_name}: {str(e)}")
            return []
    
    def _get_array_field_context(self, field_name: str, description: str) -> str:
        """Get specific context for array field extraction."""
        contexts = {
            "auxiliary_power_consumed": "Return as JSON array: [{'value': 'percentage', 'year': 'YYYY'}]",
            "emission_factor": "Return as JSON array: [{'value': 'kg_CO2e_per_kWh', 'year': 'YYYY'}]",
            "fuel_type": "Return as JSON array: [{'fuel': 'fuel_name', 'type': 'fuel_subtype', 'years_percentage': {'YYYY': 'percentage'}}]",
            "gross_power_generation": "Return as JSON array: [{'value': 'MWh_or_GWh', 'year': 'YYYY'}]",
            "PAF": "Return as JSON array: [{'value': 'percentage', 'year': 'YYYY'}]",
            "plf": "Return as JSON array: [{'value': 'percentage', 'year': 'YYYY'}]",
            "ppa_details": "Return as JSON array with PPA contract details including capacity, dates, and respondents"
        }
        
        return contexts.get(field_name, f"Return as JSON array based on: {description}")
    
    async def _post_process_unit_data(
        self,
        unit_data: Dict[str, Any],
        plant_name: str,
        unit_id: str,
        scraped_contents: List[ScrapedContent],
        org_context: Optional[Dict[str, Any]] = None,
        plant_context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Post-process unit data to enhance and validate."""
        
        # Set unit number
        unit_data["unit_number"] = unit_id
        
        # Set plant_id from plant context
        if plant_context and plant_context.get("plant_id"):
            unit_data["plant_id"] = plant_context["plant_id"]
        
        # Enhance capacity unit
        if unit_data.get("capacity") and not unit_data.get("capacity_unit"):
            unit_data["capacity_unit"] = "MW"
        
        # Set default technology based on plant type
        if not unit_data.get("technology") and plant_context:
            plant_type = plant_context.get("plant_type", "").lower()
            if "coal" in plant_type:
                unit_data["technology"] = "Supercritical"  # Default for coal
            elif "gas" in plant_type:
                unit_data["technology"] = "Combined Cycle"  # Default for gas
        
        # Enhance fuel type if missing
        if not unit_data.get("fuel_type") and plant_context:
            plant_type = plant_context.get("plant_type", "")
            unit_data["fuel_type"] = self._get_default_fuel_type(plant_type)
        
        # Set country-specific defaults
        if org_context:
            country = org_context.get("country_name", "").lower()
            unit_data = self._set_country_defaults(unit_data, country)
        
        # Validate numeric fields
        unit_data = self._validate_numeric_fields(unit_data)
        
        return unit_data
    
    def _get_default_fuel_type(self, plant_type: str) -> List[Dict[str, Any]]:
        """Get default fuel type based on plant type."""
        plant_type_lower = plant_type.lower() if plant_type else ""
        
        if "coal" in plant_type_lower:
            return [{"fuel": "Coal", "type": "Bituminous", "years_percentage": {"2023": "100"}}]
        elif "gas" in plant_type_lower:
            return [{"fuel": "Natural Gas", "type": "Pipeline", "years_percentage": {"2023": "100"}}]
        elif "nuclear" in plant_type_lower:
            return [{"fuel": "Nuclear", "type": "Uranium", "years_percentage": {"2023": "100"}}]
        else:
            return []
    
    def _set_country_defaults(self, unit_data: Dict[str, Any], country: str) -> Dict[str, Any]:
        """Set country-specific default values."""
        
        # India-specific defaults
        if "india" in country:
            if not unit_data.get("gcv_coal"):
                unit_data["gcv_coal"] = "4000"  # Typical for Indian coal
                unit_data["gcv_coal_unit"] = "kCal/kg"
            
            if not unit_data.get("heat_rate"):
                unit_data["heat_rate"] = "2500"  # Typical for Indian coal plants
                unit_data["heat_rate_unit"] = "kCal/kWh"
        
        # US-specific defaults
        elif "united states" in country:
            if not unit_data.get("gcv_coal"):
                unit_data["gcv_coal"] = "6000"  # Typical for US coal
                unit_data["gcv_coal_unit"] = "kCal/kg"
        
        return unit_data
    
    def _validate_numeric_fields(self, unit_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and clean numeric fields."""
        numeric_fields = [
            "capacity", "unit_efficiency", "heat_rate", "gcv_coal", 
            "gcv_natural_gas", "gcv_biomass", "unit_lifetime"
        ]
        
        for field in numeric_fields:
            value = unit_data.get(field)
            if value is not None:
                try:
                    # Clean and convert to float
                    if isinstance(value, str):
                        cleaned_value = ''.join(c for c in value if c.isdigit() or c in '.-')
                        if cleaned_value:
                            unit_data[field] = float(cleaned_value)
                        else:
                            unit_data[field] = None
                    else:
                        unit_data[field] = float(value)
                except (ValueError, TypeError):
                    unit_data[field] = None
        
        return unit_data
    
    def _create_empty_unit_data(self, unit_id: str) -> Dict[str, Any]:
        """Create empty unit data structure."""
        return {
            "auxiliary_power_consumed": [],
            "boiler_type": None,
            "capacity": None,
            "capacity_unit": "MW",
            "capex_required_renovation_closed_cycle": None,
            "capex_required_renovation_closed_cycle_unit": None,
            "capex_required_renovation_open_cycle": None,
            "capex_required_renovation_open_cycle_unit": None,
            "capex_required_retrofit": None,
            "capex_required_retrofit_unit": None,
            "closed_cylce_gas_turbine_efficency": None,
            "combined_cycle_heat_rate": None,
            "commencement_date": None,
            "efficiency_loss_cofiring": None,
            "emission_factor": [],
            "fuel_type": [],
            "gcv_biomass": None,
            "gcv_biomass_unit": None,
            "gcv_coal": None,
            "gcv_coal_unit": None,
            "gcv_natural_gas": None,
            "gcv_natural_gas_unit": None,
            "gross_power_generation": [],
            "heat_rate": None,
            "heat_rate_unit": None,
            "open_cycle_gas_turbine_efficency": None,
            "open_cycle_heat_rate": None,
            "PAF": [],
            "plant_id": None,
            "plf": [],
            "ppa_details": [],
            "remaining_useful_life": None,
            "selected_biomass_type": None,
            "selected_coal_type": None,
            "technology": None,
            "unit": None,
            "unit_efficiency": None,
            "unit_lifetime": None,
            "unit_number": unit_id,
            "_extraction_metadata": {
                "extraction_level": "unit",
                "unit_id": unit_id,
                "sources_count": 0,
                "validation_score": 0.0,
                "extraction_stats": self.get_extraction_stats()
            }
        }
