"""
Organizational level extractor for power plant company and ownership information.
Extracts all fields from the org_level.json schema.
"""

import logging
from typing import Dict, List, Any, Optional

from ..core import ExtractionLevel, ScrapedContent
from .base_extractor import BaseExtractor

logger = logging.getLogger(__name__)


class OrganizationalExtractor(BaseExtractor):
    """Extractor for organizational-level power plant data."""
    
    def _get_extraction_level(self) -> ExtractionLevel:
        """Get the extraction level for this extractor."""
        return ExtractionLevel.ORGANIZATIONAL
    
    def _get_search_queries(self, plant_name: str, unit_id: Optional[str] = None) -> List[str]:
        """Get search queries for organizational information."""
        return [
            f"{plant_name} power plant company owner organization",
            f"{plant_name} power station ownership structure",
            f"{plant_name} utility company operator",
            f"{plant_name} private public government ownership",
            f"{plant_name} corporate information annual report",
            f"{plant_name} parent company subsidiary",
            f"{plant_name} financial information investor relations",
            f"{plant_name} power generation company profile"
        ]
    
    async def extract_organizational_data(self, plant_name: str) -> Dict[str, Any]:
        """
        Extract complete organizational data for a power plant.
        
        Args:
            plant_name: Name of the power plant
            
        Returns:
            Dictionary with organizational data matching org_level.json schema
        """
        logger.info(f"🏢 Starting organizational extraction for: {plant_name}")
        
        try:
            # Get search queries
            search_queries = self._get_search_queries(plant_name)
            
            # Search and scrape content
            scraped_contents = await self.search_and_scrape_for_fields(plant_name, search_queries)
            
            if not scraped_contents:
                logger.warning(f"No content found for organizational extraction of {plant_name}")
                return self._create_empty_org_data()
            
            logger.info(f"Found {len(scraped_contents)} content sources for organizational extraction")
            
            # Extract simple fields
            org_data = await self.extract_simple_fields(plant_name, scraped_contents)
            
            # Post-process and enhance data
            org_data = await self._post_process_org_data(org_data, plant_name, scraped_contents)
            
            # Validate extracted data
            validation_results = self.data_validator.validate_complete_data(
                ExtractionLevel.ORGANIZATIONAL, org_data
            )
            
            logger.info(f"Organizational extraction completed. Validation score: {validation_results['validation_score']:.1f}%")
            
            # Add metadata
            org_data["_extraction_metadata"] = {
                "extraction_level": "organizational",
                "plant_name": plant_name,
                "sources_count": len(scraped_contents),
                "validation_score": validation_results["validation_score"],
                "extraction_stats": self.get_extraction_stats()
            }
            
            return org_data
            
        except Exception as e:
            logger.error(f"Organizational extraction failed for {plant_name}: {str(e)}")
            return self._create_empty_org_data()
    
    async def _post_process_org_data(
        self, 
        org_data: Dict[str, Any], 
        plant_name: str, 
        scraped_contents: List[ScrapedContent]
    ) -> Dict[str, Any]:
        """Post-process organizational data to enhance and validate."""
        
        # Enhance organization name if not found
        if not org_data.get("organization_name"):
            org_data["organization_name"] = await self._extract_organization_name(
                plant_name, scraped_contents
            )
        
        # Enhance country information
        if not org_data.get("country_name"):
            org_data["country_name"] = await self._extract_country_info(
                plant_name, scraped_contents
            )
        
        # Set currency based on country
        if org_data.get("country_name") and not org_data.get("currency_in"):
            org_data["currency_in"] = self._get_currency_for_country(org_data["country_name"])
        
        # Enhance financial year format
        if not org_data.get("financial_year"):
            org_data["financial_year"] = self._get_financial_year_format(
                org_data.get("country_name")
            )
        
        # Validate and enhance plant types
        if org_data.get("plant_types"):
            org_data["plant_types"] = self._standardize_plant_types(org_data["plant_types"])
        
        # Ensure plants_count is numeric
        if org_data.get("plants_count"):
            try:
                org_data["plants_count"] = int(str(org_data["plants_count"]).strip())
            except (ValueError, TypeError):
                org_data["plants_count"] = 1  # Default to 1 if conversion fails
        
        return org_data
    
    async def _extract_organization_name(
        self, 
        plant_name: str, 
        scraped_contents: List[ScrapedContent]
    ) -> Optional[str]:
        """Extract organization name using targeted search."""
        try:
            # Look for organization-specific patterns in content
            for content in scraped_contents:
                # Use OpenAI to extract organization name specifically
                result = await self.openai_client.extract_field(
                    field_name="organization_name",
                    content=content.content,
                    context=f"Extract the name of the company or organization that owns and operates {plant_name}. Look for the parent company, not subsidiaries or contractors.",
                    document_type=content.document_type
                )
                
                if result.extracted_value and result.confidence_score > 0.6:
                    return result.extracted_value
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to extract organization name: {str(e)}")
            return None
    
    async def _extract_country_info(
        self, 
        plant_name: str, 
        scraped_contents: List[ScrapedContent]
    ) -> Optional[str]:
        """Extract country information."""
        try:
            for content in scraped_contents:
                result = await self.openai_client.extract_field(
                    field_name="country_name",
                    content=content.content,
                    context=f"Extract the full country name where {plant_name} is located. Return only the country name.",
                    document_type=content.document_type
                )
                
                if result.extracted_value and result.confidence_score > 0.7:
                    return result.extracted_value
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to extract country info: {str(e)}")
            return None
    
    def _get_currency_for_country(self, country_name: str) -> str:
        """Get currency code for a country."""
        country_currency_map = {
            "india": "INR",
            "united states": "USD",
            "united kingdom": "GBP",
            "germany": "EUR",
            "france": "EUR",
            "japan": "JPY",
            "china": "CNY",
            "australia": "AUD",
            "canada": "CAD",
            "south africa": "ZAR",
            "brazil": "BRL"
        }
        
        country_lower = country_name.lower() if country_name else ""
        
        for country, currency in country_currency_map.items():
            if country in country_lower:
                return currency
        
        return "USD"  # Default to USD
    
    def _get_financial_year_format(self, country_name: str) -> str:
        """Get financial year format for a country."""
        country_fy_map = {
            "india": "04-03",  # April to March
            "united states": "01-12",  # January to December
            "united kingdom": "04-03",  # April to March
            "australia": "07-06",  # July to June
            "japan": "04-03",  # April to March
            "canada": "01-12",  # January to December
        }
        
        country_lower = country_name.lower() if country_name else ""
        
        for country, fy_format in country_fy_map.items():
            if country in country_lower:
                return fy_format
        
        return "01-12"  # Default to calendar year
    
    def _standardize_plant_types(self, plant_types: Any) -> List[str]:
        """Standardize plant types to consistent format."""
        if isinstance(plant_types, str):
            # Convert string to list
            types = [t.strip() for t in plant_types.split(',')]
        elif isinstance(plant_types, list):
            types = [str(t).strip() for t in plant_types]
        else:
            return ["Coal"]  # Default
        
        # Standardize type names
        standardized = []
        for plant_type in types:
            plant_type_lower = plant_type.lower()
            
            if any(term in plant_type_lower for term in ['coal', 'thermal']):
                standardized.append("Coal")
            elif any(term in plant_type_lower for term in ['gas', 'natural gas', 'ccgt', 'ocgt']):
                standardized.append("Natural Gas")
            elif any(term in plant_type_lower for term in ['nuclear']):
                standardized.append("Nuclear")
            elif any(term in plant_type_lower for term in ['solar', 'photovoltaic', 'pv']):
                standardized.append("Solar")
            elif any(term in plant_type_lower for term in ['wind']):
                standardized.append("Wind")
            elif any(term in plant_type_lower for term in ['hydro', 'hydroelectric']):
                standardized.append("Hydro")
            elif any(term in plant_type_lower for term in ['biomass', 'biofuel']):
                standardized.append("Biomass")
            else:
                standardized.append(plant_type.title())
        
        return list(set(standardized))  # Remove duplicates
    
    def _create_empty_org_data(self) -> Dict[str, Any]:
        """Create empty organizational data structure."""
        return {
            "cfpp_type": None,
            "country_name": None,
            "currency_in": None,
            "financial_year": None,
            "organization_name": None,
            "plants_count": None,
            "plant_types": None,
            "ppa_flag": None,
            "province": None,
            "_extraction_metadata": {
                "extraction_level": "organizational",
                "sources_count": 0,
                "validation_score": 0.0,
                "extraction_stats": self.get_extraction_stats()
            }
        }
