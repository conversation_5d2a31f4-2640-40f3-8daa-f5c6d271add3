"""
Plant level extractor for power plant technical and operational information.
Extracts all fields from the plant_level.json schema including nested structures.
"""

import logging
import json
from typing import Dict, List, Any, Optional

from ..core import ExtractionLevel, ScrapedContent
from .base_extractor import BaseExtractor

logger = logging.getLogger(__name__)


class PlantExtractor(BaseExtractor):
    """Extractor for plant-level power plant data."""
    
    def _get_extraction_level(self) -> ExtractionLevel:
        """Get the extraction level for this extractor."""
        return ExtractionLevel.PLANT
    
    def _get_search_queries(self, plant_name: str, unit_id: Optional[str] = None) -> List[str]:
        """Get search queries for plant information."""
        return [
            f"{plant_name} technical specifications capacity MW",
            f"{plant_name} power plant location coordinates address",
            f"{plant_name} grid connection transmission substation",
            f"{plant_name} power purchase agreement PPA contract",
            f"{plant_name} operational units generators list",
            f"{plant_name} plant specifications technical details",
            f"{plant_name} electricity transmission infrastructure",
            f"{plant_name} power plant units capacity details"
        ]
    
    async def extract_plant_data(
        self, 
        plant_name: str, 
        org_context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Extract complete plant data for a power plant.
        
        Args:
            plant_name: Name of the power plant
            org_context: Optional organizational context data
            
        Returns:
            Dictionary with plant data matching plant_level.json schema
        """
        logger.info(f"🏭 Starting plant extraction for: {plant_name}")
        
        try:
            # Get search queries
            search_queries = self._get_search_queries(plant_name)
            
            # Search and scrape content
            scraped_contents = await self.search_and_scrape_for_fields(plant_name, search_queries)
            
            if not scraped_contents:
                logger.warning(f"No content found for plant extraction of {plant_name}")
                return self._create_empty_plant_data()
            
            logger.info(f"Found {len(scraped_contents)} content sources for plant extraction")
            
            # Extract simple fields
            plant_data = await self.extract_simple_fields(plant_name, scraped_contents)
            
            # Extract nested structures
            plant_data = await self._extract_nested_structures(
                plant_data, plant_name, scraped_contents, org_context
            )
            
            # Post-process and enhance data
            plant_data = await self._post_process_plant_data(
                plant_data, plant_name, scraped_contents, org_context
            )
            
            # Validate extracted data
            validation_results = self.data_validator.validate_complete_data(
                ExtractionLevel.PLANT, plant_data
            )
            
            logger.info(f"Plant extraction completed. Validation score: {validation_results['validation_score']:.1f}%")
            
            # Add metadata
            plant_data["_extraction_metadata"] = {
                "extraction_level": "plant",
                "plant_name": plant_name,
                "sources_count": len(scraped_contents),
                "validation_score": validation_results["validation_score"],
                "extraction_stats": self.get_extraction_stats()
            }
            
            return plant_data
            
        except Exception as e:
            logger.error(f"Plant extraction failed for {plant_name}: {str(e)}")
            return self._create_empty_plant_data()
    
    async def _extract_nested_structures(
        self,
        plant_data: Dict[str, Any],
        plant_name: str,
        scraped_contents: List[ScrapedContent],
        org_context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Extract nested structures like grid_connectivity_maps and ppa_details."""
        
        # Extract grid connectivity maps
        plant_data["grid_connectivity_maps"] = await self._extract_grid_connectivity(
            plant_name, scraped_contents
        )
        
        # Extract PPA details
        plant_data["ppa_details"] = await self._extract_ppa_details(
            plant_name, scraped_contents, org_context
        )
        
        # Extract units list
        plant_data["units_id"] = await self._extract_units_list(
            plant_name, scraped_contents
        )
        
        return plant_data
    
    async def _extract_grid_connectivity(
        self, 
        plant_name: str, 
        scraped_contents: List[ScrapedContent]
    ) -> List[Dict[str, Any]]:
        """Extract grid connectivity maps information."""
        try:
            logger.info(f"Extracting grid connectivity for {plant_name}")
            
            for content in scraped_contents:
                # Extract grid connectivity information
                result = await self.openai_client.extract_field(
                    field_name="grid_connectivity_maps",
                    content=content.content,
                    context=f"""Extract grid connectivity information for {plant_name}. 
                    Look for substation details, transmission connections, voltage levels, and capacity.
                    Return as JSON array with structure: [{{"details": [{{"substation_name": "name", "substation_type": "type", "capacity": "MW", "latitude": "lat", "longitude": "long", "projects": [{{"distance": "km"}}]}}]}}]
                    If no specific information found, return empty array [].""",
                    document_type=content.document_type
                )
                
                if result.extracted_value and result.confidence_score > 0.4:
                    try:
                        if isinstance(result.extracted_value, str):
                            grid_data = json.loads(result.extracted_value)
                        else:
                            grid_data = result.extracted_value
                        
                        if isinstance(grid_data, list) and grid_data:
                            return grid_data
                    except (json.JSONDecodeError, TypeError):
                        continue
            
            return []
            
        except Exception as e:
            logger.error(f"Failed to extract grid connectivity: {str(e)}")
            return []
    
    async def _extract_ppa_details(
        self, 
        plant_name: str, 
        scraped_contents: List[ScrapedContent],
        org_context: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Extract Power Purchase Agreement details."""
        try:
            logger.info(f"Extracting PPA details for {plant_name}")
            
            for content in scraped_contents:
                # Extract PPA information
                result = await self.openai_client.extract_field(
                    field_name="ppa_details",
                    content=content.content,
                    context=f"""Extract Power Purchase Agreement (PPA) details for {plant_name}.
                    Look for contract information, capacity, duration, start/end dates, and respondent/buyer details.
                    Return as JSON array with structure: [{{"capacity": "MW", "capacity_unit": "MW", "start_date": "YYYY-MM-DD", "end_date": "YYYY-MM-DD", "tenure": "years", "tenure_type": "Years", "respondents": [{{"name": "buyer_name", "capacity": "MW", "price": "price", "price_unit": "unit", "currency": "currency"}}]}}]
                    If no PPA information found, return empty array [].""",
                    document_type=content.document_type
                )
                
                if result.extracted_value and result.confidence_score > 0.4:
                    try:
                        if isinstance(result.extracted_value, str):
                            ppa_data = json.loads(result.extracted_value)
                        else:
                            ppa_data = result.extracted_value
                        
                        if isinstance(ppa_data, list) and ppa_data:
                            return ppa_data
                    except (json.JSONDecodeError, TypeError):
                        continue
            
            return []
            
        except Exception as e:
            logger.error(f"Failed to extract PPA details: {str(e)}")
            return []
    
    async def _extract_units_list(
        self, 
        plant_name: str, 
        scraped_contents: List[ScrapedContent]
    ) -> List[str]:
        """Extract list of operational units."""
        try:
            logger.info(f"Extracting units list for {plant_name}")
            
            for content in scraped_contents:
                # Extract units information
                result = await self.openai_client.extract_field(
                    field_name="units_id",
                    content=content.content,
                    context=f"""Extract the list of operational units/generators for {plant_name}.
                    Look for unit numbers, generator IDs, or operational units.
                    Return as JSON array of strings like ["1", "2", "3"] or ["Unit 1", "Unit 2"].
                    If no specific units mentioned, return ["1"] for single unit plants.""",
                    document_type=content.document_type
                )
                
                if result.extracted_value and result.confidence_score > 0.4:
                    try:
                        if isinstance(result.extracted_value, str):
                            units_data = json.loads(result.extracted_value)
                        else:
                            units_data = result.extracted_value
                        
                        if isinstance(units_data, list) and units_data:
                            # Convert all to strings
                            return [str(unit) for unit in units_data]
                    except (json.JSONDecodeError, TypeError):
                        continue
            
            # Default to single unit if no information found
            return ["1"]
            
        except Exception as e:
            logger.error(f"Failed to extract units list: {str(e)}")
            return ["1"]
    
    async def _post_process_plant_data(
        self,
        plant_data: Dict[str, Any],
        plant_name: str,
        scraped_contents: List[ScrapedContent],
        org_context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Post-process plant data to enhance and validate."""
        
        # Set plant name if not extracted
        if not plant_data.get("name"):
            plant_data["name"] = plant_name
        
        # Generate plant ID if not found
        if not plant_data.get("plant_id"):
            plant_data["plant_id"] = self._generate_plant_id(plant_name)
        
        # Validate coordinates
        plant_data = self._validate_coordinates(plant_data)
        
        # Enhance plant type from org context
        if not plant_data.get("plant_type") and org_context:
            plant_types = org_context.get("plant_types", [])
            if plant_types:
                plant_data["plant_type"] = plant_types[0]  # Use first type
        
        # Ensure units_id is not empty
        if not plant_data.get("units_id"):
            plant_data["units_id"] = ["1"]
        
        return plant_data
    
    def _generate_plant_id(self, plant_name: str) -> int:
        """Generate a plant ID based on plant name."""
        # Simple hash-based ID generation
        import hashlib
        hash_object = hashlib.md5(plant_name.encode())
        hex_dig = hash_object.hexdigest()
        # Convert first 8 characters to integer
        return int(hex_dig[:8], 16) % 1000000  # Keep it reasonable
    
    def _validate_coordinates(self, plant_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and clean coordinate data."""
        lat = plant_data.get("lat")
        long = plant_data.get("long")
        
        # Validate latitude
        if lat is not None:
            try:
                lat_float = float(lat)
                if -90 <= lat_float <= 90:
                    plant_data["lat"] = lat_float
                else:
                    plant_data["lat"] = None
            except (ValueError, TypeError):
                plant_data["lat"] = None
        
        # Validate longitude
        if long is not None:
            try:
                long_float = float(long)
                if -180 <= long_float <= 180:
                    plant_data["long"] = long_float
                else:
                    plant_data["long"] = None
            except (ValueError, TypeError):
                plant_data["long"] = None
        
        return plant_data
    
    def _create_empty_plant_data(self) -> Dict[str, Any]:
        """Create empty plant data structure."""
        return {
            "grid_connectivity_maps": [],
            "lat": None,
            "long": None,
            "name": None,
            "plant_address": None,
            "plant_id": None,
            "plant_type": None,
            "ppa_details": [],
            "units_id": ["1"],
            "_extraction_metadata": {
                "extraction_level": "plant",
                "sources_count": 0,
                "validation_score": 0.0,
                "extraction_stats": self.get_extraction_stats()
            }
        }
