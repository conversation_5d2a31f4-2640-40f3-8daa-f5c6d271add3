"""
Base extractor framework providing common extraction patterns and utilities.
Serves as the foundation for all level-specific extractors.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime

from ..core import (
    config, ExtractionLevel, ExtractionResult, ScrapedContent, 
    FieldDefinition, ExtractionError
)
from ..clients import OpenAIExtractionClient, ScraperAPIClient, SearchAPIClient, PowerPlantSearchOrchestrator
from ..processors import SchemaProcessor, DataValidator, VisionProcessor

logger = logging.getLogger(__name__)


class BaseExtractor:
    """Base class for all data extractors with common functionality."""
    
    def __init__(
        self,
        openai_client: OpenAIExtractionClient,
        scraper_client: ScraperAPIClient,
        search_client: SearchAPIClient,
        schema_processor: SchemaProcessor,
        data_validator: DataValidator
    ):
        """Initialize the base extractor."""
        self.openai_client = openai_client
        self.scraper_client = scraper_client
        self.search_client = search_client
        self.schema_processor = schema_processor
        self.data_validator = data_validator
        
        # Initialize vision processor
        self.vision_processor = VisionProcessor(openai_client)
        
        # Initialize search orchestrator
        self.search_orchestrator = PowerPlantSearchOrchestrator(search_client)
        
        # Extraction statistics
        self.extraction_stats = {
            "fields_attempted": 0,
            "fields_successful": 0,
            "fields_failed": 0,
            "sources_used": set(),
            "extraction_time": 0.0
        }
    
    async def extract_field_from_content(
        self,
        field_def: FieldDefinition,
        content: ScrapedContent,
        plant_name: str,
        unit_id: Optional[str] = None
    ) -> ExtractionResult:
        """
        Extract a single field from scraped content.
        
        Args:
            field_def: Field definition from schema
            content: Scraped content to extract from
            plant_name: Name of the power plant
            unit_id: Optional unit identifier
            
        Returns:
            ExtractionResult with extracted value
        """
        try:
            self.extraction_stats["fields_attempted"] += 1
            
            # Generate extraction context
            context = self._generate_extraction_context(field_def, plant_name, unit_id)
            
            # Determine if vision processing is needed
            image_data = None
            if content.document_type.value == "pdf_scanned" and config.extraction.use_vision_processing:
                # For scanned PDFs, we would need the original PDF bytes
                # This is a placeholder - in practice, you'd pass the PDF bytes
                logger.info(f"Vision processing needed for {field_def.name} from {content.url}")
            
            # Extract field using OpenAI
            result = await self.openai_client.extract_field(
                field_name=field_def.name,
                content=content.content,
                context=context,
                document_type=content.document_type,
                image_data=image_data
            )
            
            # Update source URL
            result.source_url = content.url
            
            # Validate extraction result
            is_valid, error_msg = self.data_validator.validate_extraction_result(
                self._get_extraction_level(), field_def.name, result
            )
            
            if is_valid:
                self.extraction_stats["fields_successful"] += 1
                self.extraction_stats["sources_used"].add(content.url)
                logger.info(f"Successfully extracted {field_def.name}: {result.extracted_value}")
            else:
                self.extraction_stats["fields_failed"] += 1
                logger.warning(f"Validation failed for {field_def.name}: {error_msg}")
                result.confidence_score = 0.0  # Mark as invalid
            
            return result
            
        except Exception as e:
            self.extraction_stats["fields_failed"] += 1
            logger.error(f"Field extraction failed for {field_def.name}: {str(e)}")
            
            return ExtractionResult(
                field_name=field_def.name,
                extracted_value=None,
                confidence_score=0.0,
                source_url=content.url,
                extraction_method="failed",
                raw_content=str(e)
            )
    
    def _generate_extraction_context(
        self, 
        field_def: FieldDefinition, 
        plant_name: str, 
        unit_id: Optional[str] = None
    ) -> str:
        """Generate extraction context for a field."""
        context = f"Extract '{field_def.name}' for {plant_name}"
        if unit_id:
            context += f" Unit {unit_id}"
        
        context += f". Field description: {field_def.description}"
        
        # Add specific guidance based on field type
        if field_def.name in ['lat', 'latitude']:
            context += " Return only the numeric latitude value in decimal degrees."
        elif field_def.name in ['long', 'longitude']:
            context += " Return only the numeric longitude value in decimal degrees."
        elif 'capacity' in field_def.name.lower():
            context += " Return only the numeric capacity value in MW."
        elif 'date' in field_def.name.lower():
            context += " Return date in YYYY-MM-DD format if possible."
        elif field_def.data_type == "array":
            context += " Return as a JSON array."
        
        return context
    
    async def extract_multiple_fields_from_content(
        self,
        field_defs: List[FieldDefinition],
        content: ScrapedContent,
        plant_name: str,
        unit_id: Optional[str] = None
    ) -> Dict[str, ExtractionResult]:
        """Extract multiple fields from a single content source."""
        results = {}
        
        # Process fields in batches to avoid overwhelming the API
        batch_size = 3
        for i in range(0, len(field_defs), batch_size):
            batch = field_defs[i:i + batch_size]
            
            # Process batch concurrently
            tasks = [
                self.extract_field_from_content(field_def, content, plant_name, unit_id)
                for field_def in batch
            ]
            
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Store results
            for field_def, result in zip(batch, batch_results):
                if isinstance(result, Exception):
                    logger.error(f"Failed to extract {field_def.name}: {str(result)}")
                    results[field_def.name] = ExtractionResult(
                        field_name=field_def.name,
                        extracted_value=None,
                        confidence_score=0.0,
                        source_url=content.url,
                        extraction_method="failed",
                        raw_content=str(result)
                    )
                else:
                    results[field_def.name] = result
            
            # Rate limiting between batches
            if i + batch_size < len(field_defs):
                await asyncio.sleep(config.pipeline.extraction_delay)
        
        return results
    
    async def search_and_scrape_for_fields(
        self,
        plant_name: str,
        search_queries: List[str],
        unit_id: Optional[str] = None
    ) -> List[ScrapedContent]:
        """Search and scrape content for field extraction."""
        try:
            logger.info(f"Searching for {plant_name} with {len(search_queries)} queries")
            
            # Perform searches
            search_results = await self.search_client.multi_query_search(search_queries)
            
            # Flatten and prioritize results
            all_results = []
            seen_urls = set()
            
            for query_results in search_results.values():
                for result in query_results:
                    if result.url not in seen_urls:
                        all_results.append(result)
                        seen_urls.add(result.url)
            
            # Sort by priority
            priority_weights = config.url_priority_weights
            all_results.sort(
                key=lambda r: (-priority_weights.get(r.source_type.value, 0), r.rank)
            )
            
            # Scrape top results
            scraped_contents = []
            max_pages = config.pipeline.max_scrape_pages
            
            for i, search_result in enumerate(all_results[:max_pages]):
                try:
                    logger.info(f"Scraping {i+1}/{max_pages}: {search_result.url}")
                    
                    content = await self.scraper_client.scrape_url(search_result.url)
                    if content and len(content.content) >= config.pipeline.min_content_length:
                        content.source_type = search_result.source_type
                        scraped_contents.append(content)
                    
                    # Rate limiting
                    await asyncio.sleep(config.pipeline.search_delay)
                    
                except Exception as e:
                    logger.error(f"Failed to scrape {search_result.url}: {str(e)}")
                    continue
            
            logger.info(f"Successfully scraped {len(scraped_contents)} pages")
            return scraped_contents
            
        except Exception as e:
            logger.error(f"Search and scrape failed: {str(e)}")
            return []
    
    def _get_extraction_level(self) -> ExtractionLevel:
        """Get the extraction level for this extractor. To be overridden by subclasses."""
        raise NotImplementedError("Subclasses must implement _get_extraction_level")
    
    def _get_search_queries(self, plant_name: str, unit_id: Optional[str] = None) -> List[str]:
        """Get search queries for this extraction level. To be overridden by subclasses."""
        raise NotImplementedError("Subclasses must implement _get_search_queries")
    
    async def extract_simple_fields(
        self,
        plant_name: str,
        scraped_contents: List[ScrapedContent],
        unit_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Extract all simple (non-nested) fields for this level."""
        level = self._get_extraction_level()
        simple_fields = self.schema_processor.get_simple_fields(level)
        
        logger.info(f"Extracting {len(simple_fields)} simple fields for {level.value} level")
        
        extracted_data = {}
        field_sources = {}
        
        for field_def in simple_fields:
            best_result = None
            best_confidence = 0.0
            
            # Try extracting from each content source
            for content in scraped_contents:
                try:
                    result = await self.extract_field_from_content(
                        field_def, content, plant_name, unit_id
                    )
                    
                    if result.confidence_score > best_confidence:
                        best_result = result
                        best_confidence = result.confidence_score
                        
                except Exception as e:
                    logger.error(f"Failed to extract {field_def.name} from {content.url}: {str(e)}")
                    continue
            
            # Store best result
            if best_result and best_confidence >= config.pipeline.confidence_threshold:
                extracted_data[field_def.name] = best_result.extracted_value
                field_sources[field_def.name] = {
                    "source_url": best_result.source_url,
                    "confidence": best_confidence,
                    "extraction_method": best_result.extraction_method
                }
            else:
                extracted_data[field_def.name] = None
                field_sources[field_def.name] = {
                    "source_url": "not_found",
                    "confidence": 0.0,
                    "extraction_method": "failed"
                }
        
        # Store source information for tracking
        extracted_data["_field_sources"] = field_sources
        
        return extracted_data
    
    def get_extraction_stats(self) -> Dict[str, Any]:
        """Get extraction statistics."""
        stats = self.extraction_stats.copy()
        stats["sources_used"] = list(stats["sources_used"])
        stats["success_rate"] = (
            stats["fields_successful"] / stats["fields_attempted"] * 100
            if stats["fields_attempted"] > 0 else 0
        )
        return stats
