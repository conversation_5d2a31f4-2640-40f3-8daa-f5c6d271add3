"""
Schema processor for parsing and analyzing JSON schemas.
Converts schema definitions into structured field definitions for extraction.
"""

import json
import logging
from typing import Dict, List, Any, Optional
from pathlib import Path

try:
    from ..core import (
        config, FieldDefinition, SchemaDefinition, ExtractionLevel,
        SchemaError
    )
except ImportError:
    # Fallback for direct execution
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent.parent))
    from core import (
        config, FieldDefinition, SchemaDefinition, ExtractionLevel,
        SchemaError
    )

logger = logging.getLogger(__name__)


class SchemaProcessor:
    """Processes JSON schemas and converts them to structured field definitions."""
    
    def __init__(self):
        """Initialize the schema processor."""
        self.schemas: Dict[ExtractionLevel, SchemaDefinition] = {}
        self._load_all_schemas()
    
    def _load_all_schemas(self):
        """Load all schema files."""
        schema_files = {
            ExtractionLevel.ORGANIZATIONAL: config.extraction.org_schema_path,
            ExtractionLevel.PLANT: config.extraction.plant_schema_path,
            ExtractionLevel.UNIT: config.extraction.unit_schema_path
        }
        
        for level, schema_path in schema_files.items():
            try:
                self.schemas[level] = self._load_schema(schema_path, level)
                logger.info(f"Loaded {level.value} schema with {self.schemas[level].total_fields} fields")
            except Exception as e:
                raise SchemaError(f"Failed to load {level.value} schema", schema_file=schema_path)
    
    def _load_schema(self, schema_path: str, level: ExtractionLevel) -> SchemaDefinition:
        """Load and parse a single schema file."""
        try:
            with open(schema_path, 'r', encoding='utf-8') as f:
                schema_data = json.load(f)
            
            fields = self._parse_schema_fields(schema_data)
            
            return SchemaDefinition(
                level=level,
                fields=fields
            )
            
        except FileNotFoundError:
            raise SchemaError(f"Schema file not found: {schema_path}", schema_file=schema_path)
        except json.JSONDecodeError as e:
            raise SchemaError(f"Invalid JSON in schema file: {str(e)}", schema_file=schema_path)
    
    def _parse_schema_fields(self, schema_data: Dict[str, Any]) -> List[FieldDefinition]:
        """Parse schema data into field definitions."""
        fields = []
        
        for field_name, field_info in schema_data.items():
            field_def = self._create_field_definition(field_name, field_info)
            fields.append(field_def)
        
        return fields
    
    def _create_field_definition(self, field_name: str, field_info: Any) -> FieldDefinition:
        """Create a field definition from schema information."""
        if isinstance(field_info, str):
            # Simple field with description
            return FieldDefinition(
                name=field_name,
                description=field_info,
                data_type="string",
                is_required=True,
                is_nested=False
            )
        elif isinstance(field_info, list):
            # Array field - analyze first element for structure
            if field_info and isinstance(field_info[0], dict):
                # Nested array structure
                nested_fields = []
                for nested_name, nested_info in field_info[0].items():
                    nested_field = self._create_field_definition(nested_name, nested_info)
                    nested_fields.append(nested_field)
                
                return FieldDefinition(
                    name=field_name,
                    description=f"Array of {field_name} objects",
                    data_type="array",
                    is_required=True,
                    is_nested=True,
                    nested_fields=nested_fields
                )
            else:
                # Simple array
                description = field_info[0] if field_info else f"Array of {field_name} values"
                return FieldDefinition(
                    name=field_name,
                    description=description,
                    data_type="array",
                    is_required=True,
                    is_nested=False
                )
        elif isinstance(field_info, dict):
            # Complex nested object
            nested_fields = []
            for nested_name, nested_info in field_info.items():
                nested_field = self._create_field_definition(nested_name, nested_info)
                nested_fields.append(nested_field)
            
            return FieldDefinition(
                name=field_name,
                description=f"Complex {field_name} object",
                data_type="object",
                is_required=True,
                is_nested=True,
                nested_fields=nested_fields
            )
        else:
            # Fallback for unknown types
            return FieldDefinition(
                name=field_name,
                description=str(field_info),
                data_type="unknown",
                is_required=True,
                is_nested=False
            )
    
    def get_schema(self, level: ExtractionLevel) -> SchemaDefinition:
        """Get schema definition for a specific level."""
        if level not in self.schemas:
            raise SchemaError(f"Schema not loaded for level: {level.value}")
        return self.schemas[level]
    
    def get_field_definition(self, level: ExtractionLevel, field_name: str) -> Optional[FieldDefinition]:
        """Get a specific field definition."""
        schema = self.get_schema(level)
        
        for field in schema.fields:
            if field.name == field_name:
                return field
            
            # Check nested fields
            if field.is_nested:
                for nested_field in field.nested_fields:
                    if nested_field.name == field_name:
                        return nested_field
        
        return None
    
    def get_all_field_names(self, level: ExtractionLevel) -> List[str]:
        """Get all field names for a level, including nested fields."""
        schema = self.get_schema(level)
        field_names = []
        
        for field in schema.fields:
            field_names.append(field.name)
            
            if field.is_nested:
                for nested_field in field.nested_fields:
                    field_names.append(f"{field.name}.{nested_field.name}")
        
        return field_names
    
    def get_simple_fields(self, level: ExtractionLevel) -> List[FieldDefinition]:
        """Get only simple (non-nested) fields for a level."""
        schema = self.get_schema(level)
        return [field for field in schema.fields if not field.is_nested]
    
    def get_nested_fields(self, level: ExtractionLevel) -> List[FieldDefinition]:
        """Get only nested fields for a level."""
        schema = self.get_schema(level)
        return [field for field in schema.fields if field.is_nested]
    
    def generate_extraction_context(self, level: ExtractionLevel, field_name: str) -> str:
        """Generate extraction context for a specific field."""
        field_def = self.get_field_definition(level, field_name)
        if not field_def:
            return f"Extract information related to {field_name}"
        
        return field_def.generate_search_context()
    
    def validate_extracted_data(self, level: ExtractionLevel, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate extracted data against schema structure."""
        schema = self.get_schema(level)
        validated_data = {}
        
        for field in schema.fields:
            field_value = data.get(field.name)
            
            if field.is_nested and field_value:
                # Validate nested structure
                if field.data_type == "array" and isinstance(field_value, list):
                    validated_data[field.name] = field_value
                elif field.data_type == "object" and isinstance(field_value, dict):
                    validated_data[field.name] = field_value
                else:
                    logger.warning(f"Invalid structure for nested field {field.name}")
                    validated_data[field.name] = field_value
            else:
                # Simple field
                validated_data[field.name] = field_value
        
        return validated_data
    
    def get_schema_summary(self) -> Dict[str, Any]:
        """Get a summary of all loaded schemas."""
        summary = {}
        
        for level, schema in self.schemas.items():
            simple_fields = len([f for f in schema.fields if not f.is_nested])
            nested_fields = len([f for f in schema.fields if f.is_nested])
            
            summary[level.value] = {
                "total_fields": schema.total_fields,
                "simple_fields": simple_fields,
                "nested_fields": nested_fields,
                "field_names": [f.name for f in schema.fields]
            }
        
        return summary
