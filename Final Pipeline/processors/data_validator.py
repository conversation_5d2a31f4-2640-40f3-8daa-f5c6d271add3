"""
Data validator for validating extracted data against schemas.
Ensures data quality and schema compliance.
"""

import logging
import re
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime

try:
    from ..core import (
        ExtractionLevel, ExtractionResult, ValidationError
    )
    from .schema_processor import SchemaProcessor
except ImportError:
    # Fallback for direct execution
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent.parent))
    from core import (
        ExtractionLevel, ExtractionResult, ValidationError
    )
    from processors.schema_processor import SchemaProcessor

logger = logging.getLogger(__name__)


class DataValidator:
    """Validates extracted data against schema definitions."""
    
    def __init__(self, schema_processor: SchemaProcessor):
        """Initialize the data validator."""
        self.schema_processor = schema_processor
    
    def validate_extraction_result(
        self, 
        level: ExtractionLevel, 
        field_name: str, 
        result: ExtractionResult
    ) -> Tuple[bool, Optional[str]]:
        """
        Validate a single extraction result.
        
        Args:
            level: Extraction level
            field_name: Name of the field
            result: Extraction result to validate
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            field_def = self.schema_processor.get_field_definition(level, field_name)
            if not field_def:
                return False, f"Field {field_name} not found in {level.value} schema"
            
            # Check confidence threshold
            if result.confidence_score < 0.3:  # Minimum confidence threshold
                return False, f"Confidence score too low: {result.confidence_score}"
            
            # Validate value based on field type and description
            if result.extracted_value is None:
                return True, None  # Allow null values
            
            # Type-specific validation
            validation_result = self._validate_field_value(field_def, result.extracted_value)
            return validation_result
            
        except Exception as e:
            logger.error(f"Validation error for {field_name}: {str(e)}")
            return False, f"Validation error: {str(e)}"
    
    def _validate_field_value(self, field_def, value: Any) -> Tuple[bool, Optional[str]]:
        """Validate a field value based on its definition."""
        field_name = field_def.name
        description = field_def.description.lower()
        
        # Numeric fields validation
        if self._is_numeric_field(field_name, description):
            return self._validate_numeric_value(field_name, value)
        
        # Date fields validation
        elif self._is_date_field(field_name, description):
            return self._validate_date_value(field_name, value)
        
        # Coordinate fields validation
        elif self._is_coordinate_field(field_name, description):
            return self._validate_coordinate_value(field_name, value)
        
        # Currency fields validation
        elif self._is_currency_field(field_name, description):
            return self._validate_currency_value(field_name, value)
        
        # Array fields validation
        elif field_def.data_type == "array":
            return self._validate_array_value(field_name, value)
        
        # String fields validation
        else:
            return self._validate_string_value(field_name, value)
    
    def _is_numeric_field(self, field_name: str, description: str) -> bool:
        """Check if field should contain numeric values."""
        numeric_indicators = [
            'capacity', 'mw', 'kw', 'percentage', 'factor', 'efficiency',
            'rate', 'count', 'number', 'value', 'generation', 'consumption',
            'price', 'cost', 'capex', 'gcv', 'distance', 'lifetime', 'tenure'
        ]
        
        field_lower = field_name.lower()
        return any(indicator in field_lower or indicator in description 
                  for indicator in numeric_indicators)
    
    def _is_date_field(self, field_name: str, description: str) -> bool:
        """Check if field should contain date values."""
        date_indicators = ['date', 'year', 'time', 'financial_year']
        field_lower = field_name.lower()
        return any(indicator in field_lower or indicator in description 
                  for indicator in date_indicators)
    
    def _is_coordinate_field(self, field_name: str, description: str) -> bool:
        """Check if field should contain coordinate values."""
        coord_indicators = ['lat', 'long', 'latitude', 'longitude', 'coordinate']
        field_lower = field_name.lower()
        return any(indicator in field_lower or indicator in description 
                  for indicator in coord_indicators)
    
    def _is_currency_field(self, field_name: str, description: str) -> bool:
        """Check if field should contain currency values."""
        currency_indicators = ['currency', 'usd', 'inr', 'eur', 'price_unit']
        field_lower = field_name.lower()
        return any(indicator in field_lower or indicator in description 
                  for indicator in currency_indicators)
    
    def _validate_numeric_value(self, field_name: str, value: Any) -> Tuple[bool, Optional[str]]:
        """Validate numeric values."""
        try:
            # Try to convert to float
            if isinstance(value, str):
                # Remove common non-numeric characters
                cleaned_value = re.sub(r'[^\d.-]', '', value)
                if not cleaned_value:
                    return False, f"No numeric value found in: {value}"
                numeric_value = float(cleaned_value)
            else:
                numeric_value = float(value)
            
            # Basic range validation
            if 'capacity' in field_name.lower() and numeric_value < 0:
                return False, f"Capacity cannot be negative: {numeric_value}"
            
            if 'efficiency' in field_name.lower() and (numeric_value < 0 or numeric_value > 100):
                return False, f"Efficiency should be 0-100%: {numeric_value}"
            
            return True, None
            
        except (ValueError, TypeError):
            return False, f"Invalid numeric value: {value}"
    
    def _validate_date_value(self, field_name: str, value: Any) -> Tuple[bool, Optional[str]]:
        """Validate date values."""
        if not isinstance(value, str):
            return False, f"Date value should be string: {value}"
        
        # Check for year format
        if 'year' in field_name.lower():
            if re.match(r'^\d{4}$', value):
                year = int(value)
                if 1900 <= year <= 2100:
                    return True, None
                else:
                    return False, f"Year out of reasonable range: {year}"
        
        # Check for ISO date format
        if re.match(r'^\d{4}-\d{2}-\d{2}', value):
            return True, None
        
        # Check for financial year format (MM-MM)
        if 'financial_year' in field_name.lower():
            if re.match(r'^\d{2}-\d{2}$', value):
                return True, None
        
        return True, None  # Allow other date formats for now
    
    def _validate_coordinate_value(self, field_name: str, value: Any) -> Tuple[bool, Optional[str]]:
        """Validate coordinate values."""
        try:
            coord_value = float(value)
            
            if 'lat' in field_name.lower():
                if -90 <= coord_value <= 90:
                    return True, None
                else:
                    return False, f"Latitude out of range [-90, 90]: {coord_value}"
            
            elif 'long' in field_name.lower():
                if -180 <= coord_value <= 180:
                    return True, None
                else:
                    return False, f"Longitude out of range [-180, 180]: {coord_value}"
            
            return True, None
            
        except (ValueError, TypeError):
            return False, f"Invalid coordinate value: {value}"
    
    def _validate_currency_value(self, field_name: str, value: Any) -> Tuple[bool, Optional[str]]:
        """Validate currency values."""
        if not isinstance(value, str):
            return False, f"Currency should be string: {value}"
        
        # Common currency codes
        valid_currencies = ['USD', 'INR', 'EUR', 'GBP', 'JPY', 'CNY', 'AUD', 'CAD']
        
        if value.upper() in valid_currencies:
            return True, None
        
        # Allow currency units like $/MWh, INR/kW-year
        if re.match(r'^[A-Z]{3}/[A-Za-z-]+$', value):
            return True, None
        
        return True, None  # Allow other formats for now
    
    def _validate_array_value(self, field_name: str, value: Any) -> Tuple[bool, Optional[str]]:
        """Validate array values."""
        if not isinstance(value, list):
            return False, f"Array field should be list: {value}"
        
        if len(value) == 0:
            return True, None  # Allow empty arrays
        
        return True, None
    
    def _validate_string_value(self, field_name: str, value: Any) -> Tuple[bool, Optional[str]]:
        """Validate string values."""
        if value is None:
            return True, None
        
        if not isinstance(value, str):
            # Try to convert to string
            try:
                str_value = str(value)
                if len(str_value.strip()) == 0:
                    return False, f"Empty string value for {field_name}"
                return True, None
            except:
                return False, f"Cannot convert to string: {value}"
        
        if len(value.strip()) == 0:
            return False, f"Empty string value for {field_name}"
        
        return True, None
    
    def validate_complete_data(
        self, 
        level: ExtractionLevel, 
        data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Validate complete extracted data for a level.
        
        Args:
            level: Extraction level
            data: Complete extracted data
            
        Returns:
            Dictionary with validation results
        """
        validation_results = {
            "valid_fields": [],
            "invalid_fields": [],
            "missing_fields": [],
            "total_fields": 0,
            "validation_score": 0.0
        }
        
        schema = self.schema_processor.get_schema(level)
        validation_results["total_fields"] = len(schema.fields)
        
        for field in schema.fields:
            field_name = field.name
            
            if field_name in data:
                # Create mock extraction result for validation
                mock_result = ExtractionResult(
                    field_name=field_name,
                    extracted_value=data[field_name],
                    confidence_score=1.0,  # Assume high confidence for validation
                    source_url="validation",
                    extraction_method="validation"
                )
                
                is_valid, error_msg = self.validate_extraction_result(level, field_name, mock_result)
                
                if is_valid:
                    validation_results["valid_fields"].append(field_name)
                else:
                    validation_results["invalid_fields"].append({
                        "field": field_name,
                        "error": error_msg,
                        "value": data[field_name]
                    })
            else:
                validation_results["missing_fields"].append(field_name)
        
        # Calculate validation score
        valid_count = len(validation_results["valid_fields"])
        total_count = validation_results["total_fields"]
        validation_results["validation_score"] = (valid_count / total_count) * 100 if total_count > 0 else 0
        
        return validation_results
