"""
Vision processor for handling scanned PDFs and image-based documents.
Leverages OpenAI's vision capabilities for text extraction from images.
"""

import logging
import io
import base64
from typing import Optional, List, Dict, Any, Tuple
from PIL import Image
import PyPDF2
import pdf2image

from ..core import (
    config, ScrapedContent, DocumentType, 
    VisionProcessingError, ContentProcessingError
)
from ..clients import OpenAIExtractionClient

logger = logging.getLogger(__name__)


class VisionProcessor:
    """Processes scanned documents and images using vision capabilities."""
    
    def __init__(self, openai_client: OpenAIExtractionClient):
        """Initialize the vision processor."""
        self.openai_client = openai_client
        self.max_image_size = config.pipeline.max_image_size
        self.max_pages = config.pipeline.max_pages_per_extraction
    
    async def process_scanned_pdf(self, pdf_bytes: bytes, url: str) -> ScrapedContent:
        """
        Process a scanned PDF using vision capabilities.
        
        Args:
            pdf_bytes: PDF file bytes
            url: Source URL of the PDF
            
        Returns:
            ScrapedContent with extracted text
        """
        try:
            logger.info(f"Processing scanned PDF from {url}")
            
            # Convert PDF pages to images
            images = self._pdf_to_images(pdf_bytes)
            
            if not images:
                raise VisionProcessingError("No images extracted from PDF")
            
            # Process each image with vision
            extracted_texts = []
            for i, image in enumerate(images[:self.max_pages]):
                logger.info(f"Processing page {i+1}/{len(images)}")
                
                # Convert image to bytes
                image_bytes = self._image_to_bytes(image)
                
                # Extract text using vision
                text = await self._extract_text_from_image(image_bytes)
                if text:
                    extracted_texts.append(f"--- Page {i+1} ---\n{text}")
            
            # Combine all extracted text
            combined_text = "\n\n".join(extracted_texts)
            
            if not combined_text.strip():
                raise VisionProcessingError("No text extracted from scanned PDF")
            
            return ScrapedContent(
                url=url,
                content=combined_text,
                title=self._extract_title_from_url(url),
                document_type=DocumentType.PDF_SCANNED,
                is_pdf=True,
                pdf_pages=len(images)
            )
            
        except Exception as e:
            logger.error(f"Failed to process scanned PDF: {str(e)}")
            raise VisionProcessingError(
                f"Scanned PDF processing failed: {str(e)}",
                document_type="pdf_scanned",
                file_size=len(pdf_bytes)
            )
    
    def _pdf_to_images(self, pdf_bytes: bytes) -> List[Image.Image]:
        """Convert PDF pages to images."""
        try:
            # Use pdf2image to convert PDF to images
            images = pdf2image.convert_from_bytes(
                pdf_bytes,
                dpi=200,  # Good quality for text recognition
                first_page=1,
                last_page=self.max_pages,
                fmt='RGB'
            )
            
            logger.info(f"Converted PDF to {len(images)} images")
            return images
            
        except ImportError:
            logger.warning("pdf2image not available, trying alternative method")
            return self._pdf_to_images_fallback(pdf_bytes)
        except Exception as e:
            logger.error(f"PDF to image conversion failed: {str(e)}")
            return []
    
    def _pdf_to_images_fallback(self, pdf_bytes: bytes) -> List[Image.Image]:
        """Fallback method for PDF to image conversion."""
        try:
            # This is a simplified fallback - in practice, you might need
            # additional libraries or services for PDF to image conversion
            logger.warning("Using fallback PDF processing - limited functionality")
            return []
        except Exception as e:
            logger.error(f"Fallback PDF processing failed: {str(e)}")
            return []
    
    def _image_to_bytes(self, image: Image.Image) -> bytes:
        """Convert PIL Image to bytes."""
        try:
            # Resize image if too large
            if image.size[0] > self.max_image_size[0] or image.size[1] > self.max_image_size[1]:
                image = image.resize(self.max_image_size, Image.Resampling.LANCZOS)
            
            # Convert to bytes
            img_byte_arr = io.BytesIO()
            image.save(img_byte_arr, format='JPEG', quality=85)
            return img_byte_arr.getvalue()
            
        except Exception as e:
            logger.error(f"Image to bytes conversion failed: {str(e)}")
            raise VisionProcessingError(f"Image conversion failed: {str(e)}")
    
    async def _extract_text_from_image(self, image_bytes: bytes) -> str:
        """Extract text from image using OpenAI vision."""
        try:
            # Use OpenAI vision to extract text
            result = await self.openai_client.extract_field(
                field_name="document_text",
                content="Extract all visible text from this image",
                context="Extract all readable text content from the document image, maintaining structure and formatting where possible",
                document_type=DocumentType.IMAGE,
                image_data=image_bytes
            )
            
            if result.extracted_value and result.confidence_score > 0.5:
                return str(result.extracted_value)
            else:
                logger.warning("Low confidence text extraction from image")
                return ""
                
        except Exception as e:
            logger.error(f"Text extraction from image failed: {str(e)}")
            return ""
    
    async def process_image_document(self, image_bytes: bytes, url: str) -> ScrapedContent:
        """
        Process a standalone image document.
        
        Args:
            image_bytes: Image file bytes
            url: Source URL of the image
            
        Returns:
            ScrapedContent with extracted text
        """
        try:
            logger.info(f"Processing image document from {url}")
            
            # Extract text from image
            extracted_text = await self._extract_text_from_image(image_bytes)
            
            if not extracted_text.strip():
                raise VisionProcessingError("No text extracted from image")
            
            return ScrapedContent(
                url=url,
                content=extracted_text,
                title=self._extract_title_from_url(url),
                document_type=DocumentType.IMAGE,
                is_pdf=False
            )
            
        except Exception as e:
            logger.error(f"Failed to process image document: {str(e)}")
            raise VisionProcessingError(
                f"Image processing failed: {str(e)}",
                document_type="image",
                file_size=len(image_bytes)
            )
    
    def _extract_title_from_url(self, url: str) -> str:
        """Extract title from URL."""
        try:
            from urllib.parse import urlparse
            parsed = urlparse(url)
            path = parsed.path.strip('/')
            if path:
                return path.replace('/', ' - ').replace('-', ' ').replace('_', ' ').title()
            else:
                return parsed.netloc.replace('www.', '').title()
        except:
            return "Vision Processed Document"
    
    def is_scanned_pdf(self, pdf_bytes: bytes) -> bool:
        """
        Determine if a PDF is scanned (image-based) or text-based.
        
        Args:
            pdf_bytes: PDF file bytes
            
        Returns:
            True if PDF appears to be scanned
        """
        try:
            pdf_file = io.BytesIO(pdf_bytes)
            pdf_reader = PyPDF2.PdfReader(pdf_file)
            
            # Sample first few pages
            text_content = ""
            pages_to_check = min(3, len(pdf_reader.pages))
            
            for i in range(pages_to_check):
                try:
                    page_text = pdf_reader.pages[i].extract_text()
                    text_content += page_text
                except:
                    continue
            
            # If very little text is extracted, likely scanned
            text_length = len(text_content.strip())
            
            # Heuristic: if less than 100 characters per page on average, likely scanned
            avg_chars_per_page = text_length / pages_to_check if pages_to_check > 0 else 0
            
            is_scanned = avg_chars_per_page < 100
            
            logger.info(f"PDF analysis: {text_length} chars, {avg_chars_per_page:.1f} avg/page, scanned: {is_scanned}")
            
            return is_scanned
            
        except Exception as e:
            logger.error(f"Failed to analyze PDF type: {str(e)}")
            # Default to treating as scanned if analysis fails
            return True
    
    async def enhance_content_with_vision(
        self, 
        content: ScrapedContent, 
        pdf_bytes: Optional[bytes] = None
    ) -> ScrapedContent:
        """
        Enhance existing content with vision processing if applicable.
        
        Args:
            content: Existing scraped content
            pdf_bytes: Optional PDF bytes for vision processing
            
        Returns:
            Enhanced content with vision-extracted text
        """
        try:
            if content.document_type == DocumentType.PDF_SCANNED and pdf_bytes:
                # Process scanned PDF with vision
                vision_content = await self.process_scanned_pdf(pdf_bytes, content.url)
                
                # Combine original and vision-extracted content
                combined_content = f"{content.content}\n\n--- VISION ENHANCED CONTENT ---\n{vision_content.content}"
                
                return ScrapedContent(
                    url=content.url,
                    content=combined_content,
                    title=content.title,
                    document_type=DocumentType.PDF_SCANNED,
                    source_type=content.source_type,
                    is_pdf=True,
                    pdf_pages=vision_content.pdf_pages
                )
            
            return content
            
        except Exception as e:
            logger.error(f"Vision enhancement failed: {str(e)}")
            # Return original content if vision enhancement fails
            return content
