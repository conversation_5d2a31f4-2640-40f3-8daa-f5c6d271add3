"""
Universal Hierarchical Power Plant Extraction Pipeline

A comprehensive, schema-driven pipeline for extracting complete power plant data
using AI and web scraping. Extracts organizational, plant, and unit-level 
information for any power plant globally.

Key Features:
- Hierarchical extraction (Org → Plant → Unit)
- AI-powered with vision capabilities
- Schema-compliant extraction
- Universal compatibility (no hardcoding)
- Complete source attribution
- Smart caching and data flow

Usage:
    from main import UniversalPowerPlantPipeline
    
    pipeline = UniversalPowerPlantPipeline()
    result = await pipeline.extract_plant_data("Plant Name")
"""

__version__ = "1.0.0"
__author__ = "Universal Pipeline Team"
__description__ = "Universal Hierarchical Power Plant Extraction Pipeline"

# Import main components for easy access
from .main import UniversalPowerPlantPipeline
from .orchestrators import PipelineOrchestrator
from .core import config, ExtractionLevel, PipelineResult

__all__ = [
    'UniversalPowerPlantPipeline',
    'PipelineOrchestrator',
    'config',
    'ExtractionLevel',
    'PipelineResult'
]
