#!/usr/bin/env python3
"""
Test script to verify the pipeline structure and imports.
Tests the pipeline without requiring external dependencies.
"""

import sys
import os
from pathlib import Path

def test_directory_structure():
    """Test that all required directories and files exist."""
    print("🔍 Testing directory structure...")
    
    required_dirs = [
        "core", "clients", "processors", "extractors", 
        "orchestrators", "utils"
    ]
    
    required_files = [
        "main.py", "requirements.txt", "README.md",
        "org_level.json", "plant_level.json", "unit_level.json"
    ]
    
    missing_dirs = []
    missing_files = []
    
    for dir_name in required_dirs:
        if not Path(dir_name).exists():
            missing_dirs.append(dir_name)
    
    for file_name in required_files:
        if not Path(file_name).exists():
            missing_files.append(file_name)
    
    if missing_dirs:
        print(f"❌ Missing directories: {missing_dirs}")
        return False
    
    if missing_files:
        print(f"❌ Missing files: {missing_files}")
        return False
    
    print("✅ All required directories and files present")
    return True

def test_schema_files():
    """Test that schema files are valid JSON."""
    print("\n📋 Testing schema files...")
    
    schema_files = ["org_level.json", "plant_level.json", "unit_level.json"]
    
    for schema_file in schema_files:
        try:
            import json
            with open(schema_file, 'r') as f:
                schema_data = json.load(f)
            
            field_count = len(schema_data)
            print(f"✅ {schema_file}: {field_count} fields")
            
        except Exception as e:
            print(f"❌ {schema_file}: {str(e)}")
            return False
    
    return True

def test_module_structure():
    """Test that all modules have proper __init__.py files."""
    print("\n📦 Testing module structure...")
    
    modules = ["core", "clients", "processors", "extractors", "orchestrators", "utils"]
    
    for module in modules:
        init_file = Path(module) / "__init__.py"
        if not init_file.exists():
            print(f"❌ Missing {init_file}")
            return False
        else:
            print(f"✅ {module}/__init__.py exists")
    
    return True

def test_basic_imports():
    """Test basic imports without external dependencies."""
    print("\n🔧 Testing basic imports...")
    
    try:
        # Test core imports
        sys.path.insert(0, '.')
        
        # Test if we can import the core module structure
        import core
        print("✅ Core module imports")
        
        # Test schema processor (doesn't need external deps)
        from processors.schema_processor import SchemaProcessor
        schema_processor = SchemaProcessor()
        print("✅ Schema processor works")
        
        # Test that schemas load properly
        from core.models import ExtractionLevel
        org_schema = schema_processor.get_schema(ExtractionLevel.ORGANIZATIONAL)
        plant_schema = schema_processor.get_schema(ExtractionLevel.PLANT)
        unit_schema = schema_processor.get_schema(ExtractionLevel.UNIT)
        
        print(f"✅ Schemas loaded: Org({org_schema.total_fields}), Plant({plant_schema.total_fields}), Unit({unit_schema.total_fields})")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def test_configuration():
    """Test configuration loading."""
    print("\n⚙️ Testing configuration...")
    
    try:
        from core.config import config
        
        print(f"✅ Config loaded")
        print(f"   OpenAI Model: {config.api.openai_model}")
        print(f"   Max Search Results: {config.pipeline.max_search_results}")
        print(f"   Vision Processing: {config.extraction.use_vision_processing}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration error: {str(e)}")
        return False

def main():
    """Run all tests."""
    print("🚀 Universal Hierarchical Power Plant Extraction Pipeline")
    print("=" * 60)
    print("Testing pipeline structure and basic functionality...\n")
    
    tests = [
        test_directory_structure,
        test_schema_files,
        test_module_structure,
        test_basic_imports,
        test_configuration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print("❌ Test failed")
        except Exception as e:
            print(f"❌ Test error: {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Pipeline structure is correct.")
        print("\n📝 Next steps:")
        print("1. Install dependencies: pip install -r requirements.txt")
        print("2. Set up environment variables in .env file")
        print("3. Run: python main.py 'Your Plant Name'")
    else:
        print("❌ Some tests failed. Please check the issues above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
