# Financial Year Format (MM-MM)

## Overview

The `financial_year` field in the power plant data extraction pipeline now uses the **MM-MM format** to represent the fiscal year period used by organizations in different countries.

## Format Specification

- **Format**: `MM-MM` (e.g., `04-03`, `01-12`)
- **Meaning**: `Start_Month-End_Month` of the fiscal year
- **Example**: `04-03` means the fiscal year runs from April (04) to March (03)

## Common Fiscal Year Patterns by Country

### April to March (04-03)
- **India**: Financial year runs from April 1 to March 31
- **United Kingdom**: Tax year runs from April 6 to April 5 (simplified as 04-03)
- **Japan**: Fiscal year runs from April 1 to March 31
- **Canada**: Government fiscal year runs from April 1 to March 31

### January to December (01-12)
- **United States**: Most companies use calendar year (January 1 to December 31)
- **Germany**: Fiscal year typically follows calendar year
- **Switzerland**: Most companies use calendar year
- **France**: Fiscal year typically follows calendar year
- **Most EU Countries**: Calendar year is standard

### July to June (07-06)
- **Australia**: Financial year runs from July 1 to June 30
- **New Zealand**: Financial year runs from July 1 to June 30

### October to September (10-09)
- **Some US Government Agencies**: Federal fiscal year runs from October 1 to September 30

## Examples

| Country | Fiscal Year Period | MM-MM Format | Description |
|---------|-------------------|--------------|-------------|
| India | April 1 - March 31 | `04-03` | Indian financial year |
| USA | January 1 - December 31 | `01-12` | Calendar year |
| UK | April 6 - April 5 | `04-03` | UK tax year |
| Australia | July 1 - June 30 | `07-06` | Australian financial year |
| Switzerland | January 1 - December 31 | `01-12` | Calendar year |
| Japan | April 1 - March 31 | `04-03` | Japanese fiscal year |

## Implementation Details

### Extraction Logic

The pipeline uses the following logic to determine fiscal year format:

1. **Direct Pattern Matching**: Looks for explicit mentions of fiscal year periods
2. **Country-Based Inference**: Uses country information to infer standard fiscal year
3. **Content Analysis**: Analyzes financial reporting language for clues

### Validation

The system validates that:
- Format follows MM-MM pattern (e.g., `04-03`)
- Month values are between 01-12
- Both start and end months are valid

### Fallback Strategy

If fiscal year cannot be determined from content:
1. Use country-based inference from `country_name` field
2. Default patterns based on common practices
3. Return empty string if no reliable inference possible

## Testing

Use the test script to verify fiscal year extraction:

```bash
# Test fiscal year extraction for different countries
python test_financial_year.py
```

This will test extraction for:
- India (expected: 04-03)
- United States (expected: 01-12)
- United Kingdom (expected: 04-03)
- Australia (expected: 07-06)
- Switzerland (expected: 01-12)

## Usage in Pipeline

When running the pipeline, the `financial_year` field will now contain:

```json
{
  "financial_year": "04-03",  // April to March
  "country_name": "India",
  "currency_in": "INR"
}
```

## Benefits

1. **Standardized Format**: Consistent MM-MM format across all countries
2. **International Support**: Handles different fiscal year patterns globally
3. **Automated Inference**: Can infer fiscal year from country when not explicitly stated
4. **Validation**: Ensures data quality with format validation

## References

- [Wikipedia: Fiscal Year](https://en.wikipedia.org/wiki/Fiscal_year)
- Country-specific fiscal year regulations and practices
